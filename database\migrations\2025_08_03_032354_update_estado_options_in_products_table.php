<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        
        // 1. Alterar temporariamente para VARCHAR para permitir a atualização
        Schema::table('products', function (Blueprint $table) {
            $table->string('estado', 255)->change();
        });

        // Primeiro, atualize os dados existentes para os novos valores
        DB::statement("UPDATE products SET estado = 'Nova(o) com Etiqueta' WHERE estado = 'Nova com Etiqueta'");
        DB::statement("UPDATE products SET estado = 'Nova(o)' WHERE estado = 'Nova'");
        DB::statement("UPDATE products SET estado = 'Seminova(o)' WHERE estado = 'Seminova'");
        DB::statement("UPDATE products SET estado = 'Usada(o)' WHERE estado = 'Usada'");

        // Em seguida, altere a definição da coluna
        Schema::table('products', function (Blueprint $table) {
            $table->enum('estado', ['Nova(o) com Etiqueta', 'Nova(o)', 'Seminova(o)', 'Usada(o)'])->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // 1. Alterar temporariamente para VARCHAR para permitir a atualização de volta
        Schema::table('products', function (Blueprint $table) {
            $table->string('estado', 255)->change();
        });
        // Primeiro, atualize os dados para os valores antigos
        DB::statement("UPDATE products SET estado = 'Nova com Etiqueta' WHERE estado = 'Nova(o) com Etiqueta'");
        DB::statement("UPDATE products SET estado = 'Nova' WHERE estado = 'Nova(o)'");
        DB::statement("UPDATE products SET estado = 'Seminova' WHERE estado = 'Seminova(o)'");
        DB::statement("UPDATE products SET estado = 'Usada' WHERE estado = 'Usada(o)'");

        // Em seguida, reverta a definição da coluna
        Schema::table('products', function (Blueprint $table) {
            $table->enum('estado', ['Nova com Etiqueta', 'Nova', 'Seminova', 'Usada'])->change();
        });
    }
};
