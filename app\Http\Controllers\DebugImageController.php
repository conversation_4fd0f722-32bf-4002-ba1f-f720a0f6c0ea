<?php

namespace App\Http\Controllers;

use App\Models\ProductImage;
use Illuminate\Support\Facades\Storage;

class DebugImageController extends Controller
{
    public function show($id)
    {
        $image = ProductImage::findOrFail($id);

        $path = storage_path("app/public/{$image->path}");
        $previewPath = storage_path("app/public/products/debug/{$image->id}_preview.jpg");

        // Garante que o preview exista (pode gerar em outro momento via generatePreview se quiser)
        if (!file_exists($previewPath)) {
            // Nenhuma geração aqui — apenas exibição.
        }

        $dimensions = getimagesize($path);
        $naturalWidth = $dimensions[0] ?? 0;
        $naturalHeight = $dimensions[1] ?? 0;

        return view('debug.crop', [
            'image' => $image,
            'naturalWidth' => $naturalWidth,
            'naturalHeight' => $naturalHeight,
        ]);
    }
}
