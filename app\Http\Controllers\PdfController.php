<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\ProductImage;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;

class PdfController extends Controller
{
    public function view()
{
    // Carrega os produtos com imagens
    $productsRaw = Product::with('images')
        ->whereIn('status', ['ativo', 'reservado'])
        ->orderBy('categoria')
        ->get();

    // Monta o JSON completo com os dados necessários para Alpine
    $products = $productsRaw->map(function ($product) {
        return [
            'id' => $product->id,
            'marca' => $product->marca,
            'cor' => $product->cor,
            'tamanho' => $product->tamanho,
            'caracteristicas' => $product->caracteristicas,
            'estado' => $product->estado,
            'descricao' => $product->descricao,
            'valor' => $product->valor,
            'categoria' => $product->categoria,
            'status' => $product->status,
            'images' => $product->images->map(function ($img) {
                return [
                    'path' => $img->path,
                    'cropped_path' => $img->cropped_path,
                    'cache_version' => $img->cache_version,
                ];
            })->values(),
        ];
    });

    // Carrega os dados auxiliares
    $categories = $productsRaw->pluck('categoria')->unique()->sort()->values();
    $sizes = $productsRaw->pluck('tamanho')->unique()->sort()->values();
    $logoSrc = $this->getLogoSrc();

    // Retorna a view
    return view('catalog', [
        'products' => $products,
        'logoSrc' => $logoSrc,
        'isPdf' => false,
        'categories' => $categories,
        'sizes' => $sizes,
    ]);
}

    private function getProductsWithCroppedImages(bool $forPdf = true)
    {
        $products = Product::with(['images' => function ($query) {
            $query->orderBy('id')->limit(1);
        }])
        ->whereIn('status', ['ativo', 'reservado'])
        ->orderBy('categoria')
        ->get();

        if ($forPdf) {
            $manager = new ImageManager(new Driver());

            foreach ($products as $product) {
                if ($product->images->isNotEmpty()) {
                    $imageModel = $product->images->first();
                    $path = $imageModel->path;

                    if (Storage::disk('public')->exists($path)) {
                        try {
                            $img = $manager->read(Storage::disk('public')->get($path)); // Move esta linha para cá

                            // Usa a imagem cortada salva no disco, se existir
                            if ($imageModel->cropped_path && Storage::disk('public')->exists($imageModel->cropped_path)) {
                                $imageModel->cropped_src = 'data:image/png;base64,' . base64_encode(Storage::disk('public')->get($imageModel->cropped_path));
                            } else {
                                // Fallback para imagens que ainda não foram processadas ou se o cropped_path não existir
                                // Isso pode acontecer para imagens antigas que não foram re-salvas com a nova lógica
                                $scale = $imageModel->crop_scale ?? 1;
                                $x = $imageModel->crop_x ?? 0;
                                $y = $imageModel->crop_y ?? 0;

                                // Redimensiona imagem original proporcionalmente
                                $newWidth = (int) round($img->width() * $scale);
                                $newHeight = (int) round($img->height() * $scale);

                                $img->resize($newWidth, $newHeight);
                                
                                // Ajusta posição da imagem dentro do canvas (lembre que é negativo para simular translate)
                                $adjustedX = (int) round($x * $scale);
                                $adjustedY = (int) round($y * $scale);

                                // Cria canvas 200x200 com fundo branco
                                $canvas = $manager->create(200, 200)->fill('ffffff');

                                // Posiciona imagem no canvas usando as coordenadas negativas (simulando translate)
                                $canvas->place($img, 'top-left', -$adjustedX, -$adjustedY);
                                $imageModel->cropped_src = $canvas->toPng()->toDataUri();
                            }

                        } catch (\Exception $e) {
                            Log::error('Erro ao processar imagem para PDF: ' . $e->getMessage(), ['path' => $path, 'exception' => $e]);
                            $imageModel->cropped_src = $this->getPlaceholderImage();
                        }
                    } else {
                         $imageModel->cropped_src = $this->getPlaceholderImage();
                    }
                }
            }
        }

        if ($forPdf) {
            $grouped = $products->groupBy('categoria');
            $categorized = collect();

            foreach ($grouped as $category => $items) {
                $categorized[$category] = $items->chunk(2);
            }
            return $categorized;
        }

        return $products;
    }

    /**
     * Get logo source with fallback handling
     */
    private function getLogoSrc(): string
    {
        $logoPath = public_path('images/logo.jpg');

        if (file_exists($logoPath)) {
            try {
                $logoData = base64_encode(file_get_contents($logoPath));
                return 'data:image/jpeg;base64,' . $logoData;
            } catch (\Exception $e) {
                Log::warning('Erro ao carregar logo: ' . $e->getMessage());
            }
        }

        // Fallback: retorna uma imagem placeholder ou vazia
        return $this->getPlaceholderLogo();
    }

    /**
     * Generate a simple placeholder logo
     */
    private function getPlaceholderLogo(): string
    {
        // Cria um SVG simples como placeholder
        $svg = '<svg width="300" height="100" xmlns="http://www.w3.org/2000/svg">
            <rect width="300" height="100" fill="#f3f4f6" stroke="#d1d5db" stroke-width="2"/>
            <text x="150" y="55" font-family="Arial, sans-serif" font-size="16" text-anchor="middle" fill="#6b7280">
                Logo da Empresa
            </text>
        </svg>';

        return 'data:image/svg+xml;base64,' . base64_encode($svg);
    }

    private function getPlaceholderImage(): string
    {
        $svg = '<svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
            <rect width="200" height="200" fill="#e0e0e0"/>
            <text x="100" y="100" font-family="Arial" font-size="14" text-anchor="middle" alignment-baseline="middle" fill="#999">
                Imagem não disponível
            </text>
        </svg>';
        return 'data:image/svg+xml;base64,' . base64_encode($svg);
    }

    public function paginated(Request $request)
    {
        $perPage = 6;
        $page = $request->input('page', 1);

        $query = Product::with('images')
            ->whereIn('status', ['ativo', 'reservado'])
            ->orderBy('categoria');

        if ($request->filled('categories')) {
            $query->whereIn('categoria', $request->categories);
        }

        if ($request->filled('sizes')) {
            $query->whereIn('tamanho', $request->sizes);
        }

        $products = $query->skip(($page - 1) * $perPage)->take($perPage)->get();

        $data = $products->map(function ($product) {
            return [
                'id' => $product->id,
                'marca' => $product->marca,
                'cor' => $product->cor,
                'tamanho' => $product->tamanho,
                'caracteristicas' => $product->caracteristicas,
                'estado' => $product->estado,
                'descricao' => $product->descricao,
                'valor' => $product->valor,
                'categoria' => $product->categoria,
                'status' => $product->status,
                'images' => $product->images->map(function ($img) {
                    return [
                        'path' => $img->path,
                        'cropped_path' => $img->cropped_path,
                        'cache_version' => $img->cache_version,
                    ];
                })->values(),
            ];
        });

        return response()->json($data);
    }
}
