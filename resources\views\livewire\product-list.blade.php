<div class="p-4 sm:p-6 lg:p-8">
    <div class="sm:flex sm:items-center sm:justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white sm:text-3xl">Lista de Produtos</h1>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Gerencie os produtos cadastrados no sistema.</p>
        </div>

        <div class="mt-4 flex flex-col gap-4 sm:mt-0 sm:flex-row sm:items-center">
            <a
                href="{{ route('products.create') }}"
                @click.prevent="$dispatch('loading-start'); window.location.href = '{{ route('products.create') }}';"
                class="inline-block rounded-lg bg-blue-600 px-5 py-3 text-sm font-medium text-white text-center"
            >
                <svg class="animate-spin inline-block h-4 w-4 mr-2 text-white" x-show="$root.loading" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" />
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z" />
                </svg>
                <span>Cadastrar Produto</span>
            </a>
        </div>
    </div>

    <!-- Mensagem de sessão padrão (do formulário de criação/edição) -->
    @if (session()->has('message'))
        <div x-data="{ show: true }" x-show="show" x-init="setTimeout(() => show = false, 3000)" x-transition
            class="rounded-lg border border-green-300 bg-green-50 p-4 text-green-700 my-4" role="alert">
            <div class="flex items-center gap-4">
                <span class="p-2 text-white bg-green-600 rounded-full">
                    <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor" stroke-width="2">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </span>
                <p class="font-medium text-sm">{{ session('message') }}</p>
            </div>
        </div>
    @endif

    <!-- Mensagem de eventos do Alpine.js (atualização de status, exclusão) -->
    <div x-data="{ show: false, message: '' }"
         @status-updated.window="message = $event.detail.message; show = true; setTimeout(() => show = false, 3000)"
         x-cloak>
        <div x-show="show && message" x-transition
            class="rounded-lg border border-green-300 bg-green-50 p-4 text-green-700 my-4" role="alert">
            <div class="flex items-center gap-4">
                <span class="p-2 text-white bg-green-600 rounded-full">
                    <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor" stroke-width="2">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </span>
                <p class="font-medium text-sm" x-text="message"></p>
            </div>
        </div>
    </div>

    <div class="mt-8">
        <!-- Desktop Table -->
        <div class="hidden sm:block overflow-x-auto rounded-lg border border-gray-200 dark:border-gray-700">
            <table class="w-full divide-y-2 divide-gray-200 bg-white text-sm dark:divide-gray-700 dark:bg-gray-800">
                <thead class="bg-gray-50 dark:bg-gray-700 text-left">
                    <tr>
                        <th class="whitespace-nowrap px-4 py-2 font-medium text-gray-900 dark:text-white">Foto</th>
                        <th class="whitespace-nowrap px-4 py-2 font-medium text-gray-900 dark:text-white">Marca</th>
                        <th class="whitespace-nowrap px-4 py-2 font-medium text-gray-900 dark:text-white w-32">Cor</th>
                        <th class="whitespace-nowrap px-4 py-2 font-medium text-gray-900 dark:text-white">Tamanho</th>
                        <th class="whitespace-nowrap px-4 py-2 font-medium text-gray-900 dark:text-white">Valor</th>
                        <th class="whitespace-nowrap px-4 py-2 font-medium text-gray-900 dark:text-white">Status</th>
                        <th class="px-4 py-2"></th>
                    </tr>
                </thead>

                <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                    @forelse ($products as $product)
                        <tr>
                            <td class="whitespace-nowrap px-4 py-2">
                                @if ($product->images->isNotEmpty())
                                    <div style="width: 80px; height: 80px;">
                                        <img
                                            src="{{ asset('storage/' . ($product->images->first()->cropped_path ?? $product->images->first()->path)) }}?v={{ $product->images->first()->cache_version }}"
                                            alt="Thumb"
                                            class="w-full h-full object-cover rounded"
                                        >
                                    </div>
                                @endif
                            </td>
                            <td class="whitespace-nowrap px-4 py-2 font-medium text-gray-900 dark:text-white">{{ $product->marca }}</td>
                            <td class="whitespace-nowrap px-4 py-2 text-gray-700 dark:text-gray-200 w-32">{{ $product->cor }}</td>
                            <td class="whitespace-nowrap px-4 py-2 text-gray-700 dark:text-gray-200">{{ $product->tamanho }}</td>
                            <td class="whitespace-nowrap px-4 py-2 text-gray-700 dark:text-gray-200">R$ {{ number_format($product->valor, 2, ',', '.') }}</td>
                            <td class="whitespace-nowrap px-4 py-2">
                                <select
                                    wire:change="updateStatus({{ $product->id }}, $event.target.value)"
                                    class="rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                >
                                    <option value="ativo" @if($product->status == 'ativo') selected @endif>Ativo</option>
                                    <option value="inativo" @if($product->status == 'inativo') selected @endif>Inativo</option>
                                    <option value="vendido" @if($product->status == 'vendido') selected @endif>Vendido</option>
                                    <option value="reservado" @if($product->status == 'reservado') selected @endif>Reservado</option>
                                </select>
                            </td>
                            <td class="whitespace-nowrap px-4 py-2">
                                <div class="flex items-center justify-end gap-2">
                                    <a href="{{ route('products.edit', $product) }}"
                                       class="inline-block p-2 text-yellow-600 hover:text-yellow-700">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                            <path d="M17.414 2.586a2 2 0 00-2.828 0L7 10.172V13h2.828l7.586-7.586a2 2 0 000-2.828z" />
                                            <path fill-rule="evenodd" d="M2 6a2 2 0 012-2h4a1 1 0 010 2H4v10h10v-4a1 1 0 112 0v4a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" clip-rule="evenodd" />
                                        </svg>
                                    </a>
                                    <button
                                        wire:click="deleteProduct({{ $product->id }})"
                                        wire:confirm="Tem certeza que deseja excluir este produto? Esta ação não poderá ser desfeita."
                                        class="inline-block p-2 text-red-600 hover:text-red-700">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm4 0a1 1 0 012 0v6a1 1 0 11-2 0V8z" clip-rule="evenodd" />
                                        </svg>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="6" class="py-4 text-center text-gray-500 dark:text-gray-400">
                                Nenhum produto cadastrado.
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <!-- Mobile Card Layout -->
        <div class="sm:hidden grid grid-cols-1 gap-4">
            @forelse ($products as $product)
                <div class="rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-700 dark:bg-gray-800">
                    <div class="flex items-start gap-4">
                        @if ($product->images->isNotEmpty())
                            <div style="width: 80px; height: 80px; flex-shrink: 0;">
                                <img
                                    src="{{ asset('storage/' . ($product->images->first()->cropped_path ?? $product->images->first()->path)) }}?v={{ $product->images->first()->cache_version }}"
                                    alt="Thumb"
                                    class="w-full h-full object-cover rounded"
                                >
                            </div>
                        @endif

                        <div class="flex-1">
                            <h3 class="font-medium text-gray-900 dark:text-white">{{ $product->marca }}</h3>
                            <p class="text-sm text-gray-700 dark:text-gray-200">{{ $product->cor }} - {{ $product->tamanho }}</p>
                            <p class="mt-1 text-sm font-semibold text-gray-900 dark:text-white">R$ {{ number_format($product->valor, 2, ',', '.') }}</p>
                        </div>
                    </div>

                    <div class="mt-4 flex justify-between items-center gap-2">
                        <div>
                            <select
                                wire:change="updateStatus({{ $product->id }}, $event.target.value)"
                                class="rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                            >
                                <option value="ativo" @if($product->status == 'ativo') selected @endif>Ativo</option>
                                <option value="inativo" @if($product->status == 'inativo') selected @endif>Inativo</option>
                                <option value="vendido" @if($product->status == 'vendido') selected @endif>Vendido</option>
                                <option value="reservado" @if($product->status == 'reservado') selected @endif>Reservado</option>
                            </select>
                        </div>
                        <div class="flex flex-col gap-2">
                            <a href="{{ route('products.edit', $product) }}"
                               class="inline-block p-2 text-yellow-600 hover:text-yellow-700">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                    <path d="M17.414 2.586a2 2 0 00-2.828 0L7 10.172V13h2.828l7.586-7.586a2 2 0 000-2.828z" />
                                    <path fill-rule="evenodd" d="M2 6a2 2 0 012-2h4a1 1 0 010 2H4v10h10v-4a1 1 0 112 0v4a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" clip-rule="evenodd" />
                                </svg>
                            </a>
                            <button
                                wire:click="deleteProduct({{ $product->id }})"
                                wire:confirm="Tem certeza que deseja excluir este produto? Esta ação não poderá ser desfeita."
                                class="inline-block p-2 text-red-600 hover:text-red-700">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm4 0a1 1 0 012 0v6a1 1 0 11-2 0V8z" clip-rule="evenodd" />
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            @empty
                <div class="py-4 text-center text-gray-500 dark:text-gray-400">
                    Nenhum produto cadastrado.
                </div>
            @endforelse
        </div>
    </div>

    <div class="mt-4">
        {{ $products->links() }}
    </div>
</div>
