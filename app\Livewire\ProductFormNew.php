<?php

namespace App\Livewire;

use App\Models\Product;
use Livewire\Component;
use Livewire\WithFileUploads;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;
use Intervention\Image\Facades\Image;

class ProductFormNew extends Component
{
    use WithFileUploads;

    public $marca = '';
    public $cor = '';
    public $tamanho = 'M';
    public $caracteristicas = '';
    public $estado = 'Nova(o)';
    public $descricao = '';
    public $valor = '';
    public $status = 'ativo'; // Novo campo
    public $categoria = ''; // Novo campo
    public $photos = [];
    public $existingPhotos = [];
    public $isEditMode = false;
    public $product;
    public $showCropModal = false;
    public $currentPhotoIndex = 0;
    public $cropData = [];
    public $croppedPreviews = []; // Para guardar as URLs das pré-visualizações cortadas
    public $isAnalyzing = false;
    public $isSaving = false;

    protected $rules = [

        'marca' => 'string|max:255|min:2',
        'categoria' => 'required|string|max:255', // Idealmente, usar Rule::in([...])
        'cor' => 'required|string|max:255|min:2',
        'tamanho' => 'required|in:PP,P,M,G,GG,G1,Único,34,36,38,40,42,44,46,48,50',
        'caracteristicas' => 'required|string|min:5|max:1000',
        'estado' => 'required|in:Nova(o) com Etiqueta,Nova(o),Seminova(o),Usada(o)',
        'descricao' => 'required|string|min:5|max:2000',
        'valor' => 'required|string',
        'status' => 'required|in:ativo,inativo,reservado,vendido',
        'photos.*' => 'nullable|image|mimes:jpeg,jpg,png,webp|max:5120',
    ];

    protected $messages = [
        'marca.min' => 'A marca deve ter pelo menos 2 caracteres.',
        'categoria.required' => 'A categoria é obrigatória.',
        'cor.required' => 'A cor é obrigatória.',
        'cor.min' => 'A cor deve ter pelo menos 2 caracteres.',
        'tamanho.required' => 'O tamanho é obrigatório.',
        'tamanho.in' => 'O tamanho selecionado é inválido.',
        'caracteristicas.min' => 'As características devem ter pelo menos 5 caracteres.',
        'caracteristicas.max' => 'As características não podem exceder 1000 caracteres.',
        'estado.required' => 'O estado é obrigatório.',
        'estado.in' => 'O estado selecionado é inválido.',
        'descricao.min' => 'A descrição deve ter pelo menos 5 caracteres.',
        'descricao.max' => 'A descrição não pode exceder 2000 caracteres.',
        'valor.required' => 'O valor é obrigatório.',
        'status.required' => 'O status é obrigatório.',
        'status.in' => 'O status selecionado é inválido.',
        'photos.*.image' => 'O arquivo deve ser uma imagem.',
        'photos.*.mimes' => 'A imagem deve ser do tipo: jpeg, jpg, png ou webp.',
        'photos.*.max' => 'A imagem não pode ser maior que 5MB.',
    ];

    public function mount(?Product $product = null)
    {
        $this->product = $product ?? new Product();
        $this->isEditMode = $product && $product->exists;
        $this->photos = [];
        $this->existingPhotos = collect();

        $this->marca = '';
        $this->cor = '';
        $this->tamanho = '';
        $this->caracteristicas = '';
        $this->estado = '';
        $this->descricao = '';
        $this->valor = '';
        $this->status = 'ativo'; // Definir um valor padrão para o novo campo
        $this->categoria = ''; // Definir um valor padrão para o novo campo

        if ($this->isEditMode) {
            $this->marca = $product->marca ?? '';
            $this->cor = $product->cor ?? '';
            $this->tamanho = $product->tamanho ?? '';
            $this->caracteristicas = $product->caracteristicas ?? '';
            $this->estado = $product->estado ?? '';
            $this->descricao = $product->descricao ?? '';
            $this->valor = $product->valor ? number_format($product->valor, 2, ',', '.') : '';
            $this->status = $product->status ?? 'ativo'; // Carregar status existente
            $this->categoria = $product->categoria ?? ''; // Carregar categoria existente
            $this->existingPhotos = $product->images ?? collect();
        }
    }

    public function updatedPhotos()
    {
        $this->validate([
            'photos.*' => 'nullable|image|mimes:jpeg,jpg,png,webp|max:2048',
        ]);
        // Chamar a análise da imagem com IA após a validação do upload
        $this->analyzeImageWithAI();
    }

    public function analyzeImageWithAI()
    {
        Log::info('Método analyzeImageWithAI foi chamado.');

        if (empty($this->photos)) {
            Log::warning('analyzeImageWithAI chamado, mas a propriedade photos está vazia.');
            return;
        }

        // Validar a imagem antes de prosseguir
        $this->validate([
            'photos.0' => 'image|max:5120',
        ]);

        $this->isAnalyzing = true;
        Log::info('Iniciando análise de imagem com IA (após validação).');

        try {
            $image = $this->photos[0];
            $imageData = base64_encode(file_get_contents($image->getRealPath()));
            $imageMimeType = $image->getMimeType();

            $apiKey = env('GEMINI_API_KEY');
            if (!$apiKey) {
                throw new \Exception('A chave da API do Gemini não está configurada.');
            }
            
            $url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent?key={$apiKey}";

            $prompt = "Você é um especialista em catalogação de produtos de moda para um brechó. Analise a imagem desta peça de roupa e retorne um objeto JSON com as seguintes chaves e seus respectivos valores. Se não tiver certeza sobre um campo, deixe-o como uma string vazia.

            - 'marca': A marca da peça.
            - 'categoria': A categoria da peça (opções: 'Calças', 'Camisas', 'Casacos / Jaquetas', 'Coletes', 'Conjuntos', 'Lingerie', 'Macacões', 'Pijamas', 'Blusas / Croppeds', 'Saias', 'Shorts', 'Tênis / Sapatos', 'Regatas / T-shirts', 'Vestidos', 'Bijuterias', 'Bolsas', 'Chapéus / Bonés', 'Cintos', 'Óculos' e 'Relógios').
            - 'cor': A cor predominante da peça.
            - 'tamanho': O tamanho (opções: 'PP', 'P', 'M', 'G', 'GG', 'G1', 'Único', '34', '36', '38', '40', '42', '44', '46', '48' e '50').
            - 'estado': A condição da peça (opções: 'Nova(o) com Etiqueta', 'Nova(o)', 'Seminova(o)', 'Usada(o)').
            - 'descricao': Uma descrição curta e atrativa da peça (máximo 2 frases).
            - 'caracteristicas': O tipo de tecido ou material principal (ex: 'Algodão', 'Jeans', 'Seda' e etc).
            - 'valor': Um preço sugerido em Reais (BRL) com base na marca, modelo, qualidade e características da peça e pesquisa de mercado. Retorne apenas o número, sem 'R$'.

            Responda apenas com o objeto JSON, sem nenhum texto adicional ou formatação de markdown como ```json.";

            $payload = [
                'contents' => [
                    [
                        'parts' => [
                            ['text' => $prompt],
                            [
                                'inline_data' => [
                                    'mime_type' => $imageMimeType,
                                    'data' => $imageData,
                                ],
                            ],
                        ],
                    ],
                ],
            ];

            $response = Http::post($url, $payload);

            if ($response->failed()) {
                Log::error('Gemini API request failed', ['response' => $response->body()]);
                session()->flash('error', 'Falha ao comunicar com o serviço de IA.');
                return;
            }

            $result = $response->json();
            Log::info('Gemini API Response:', $result);

            if (isset($result['candidates'][0]['content']['parts'][0]['text'])) {
                $jsonText = $result['candidates'][0]['content']['parts'][0]['text'];
                // Limpar a resposta para garantir que seja um JSON válido
                $jsonText = trim(str_replace(['```json', '```'], '', $jsonText));
                $productData = json_decode($jsonText, true);

                if (json_last_error() === JSON_ERROR_NONE) {
                    $this->marca = $productData['marca'] ?? $this->marca;
                    $this->categoria = $productData['categoria'] ?? $this->categoria;
                    $this->cor = $productData['cor'] ?? $this->cor;
                    $this->tamanho = $productData['tamanho'] ?? $this->tamanho;
                    $this->estado = $productData['estado'] ?? $this->estado;
                    $this->descricao = $productData['descricao'] ?? $this->descricao;
                    $this->caracteristicas = $productData['caracteristicas'] ?? $this->caracteristicas;
                    $this->valor = isset($productData['valor']) ? (string) $productData['valor'] : $this->valor;
                    session()->flash('message', 'Análise da imagem concluída!');
                    $this->dispatch('fields-updated'); // Reintroduz o dispatch para o evento customizado
                } else {
                    Log::error('Failed to decode JSON from Gemini response', ['json_text' => $jsonText]);
                    session()->flash('error', 'A resposta da IA não estava no formato JSON esperado.');
                }
            } else {
                Log::error('Unexpected Gemini API response structure', ['response' => $result]);
                $errorMessage = $result['error']['message'] ?? 'Estrutura de resposta da IA inesperada.';
                session()->flash('error', 'Erro da API: ' . $errorMessage);
            }

        } catch (\Exception $e) {
            Log::error('Error in analyzeImageWithAI', ['message' => $e->getMessage()]);
            session()->flash('error', 'Ocorreu um erro: ' . $e->getMessage());
        } finally {
            $this->isAnalyzing = false;
        }
    }

    public function save()
    {
        if ($this->isSaving) {
            return;
        }

        $this->isSaving = true;

        try {
            $this->validate();

            $valorNormalizado = $this->normalizePrice($this->valor);

            $this->product->fill([
                'marca' => trim($this->marca),
                'categoria' => trim($this->categoria),
                'cor' => trim($this->cor),
                'tamanho' => $this->tamanho,
                'caracteristicas' => trim($this->caracteristicas),
                'estado' => $this->estado,
                'descricao' => trim($this->descricao),
                'valor' => $valorNormalizado,
                'status' => $this->status,
            ]);

            $this->product->save();

            foreach ($this->photos as $index => $photo) {
                $path = $photo->store('products', 'public');

                $imageData = [
                    'path' => $path,
                    'cache_version' => now()->timestamp,
                ];

                if (isset($this->cropData[$index])) {
                    $imageData = array_merge($imageData, [
                        'crop_x' => $this->cropData[$index]['x'] ?? 0,
                        'crop_y' => $this->cropData[$index]['y'] ?? 0,
                        'crop_scale' => $this->cropData[$index]['scale'] ?? 1,
                        'crop_data_x' => $this->cropData[$index]['crop_data_x'] ?? 0,
                        'crop_data_y' => $this->cropData[$index]['crop_data_y'] ?? 0,
                        'crop_data_width' => $this->cropData[$index]['crop_data_width'] ?? 400,
                        'crop_data_height' => $this->cropData[$index]['crop_data_height'] ?? 400,
                    ]);
                }

                $image = $this->product->images()->create($imageData);

                if (isset($this->cropData[$index])) {
                    $image->generateCroppedImage();
                }
            }

            session()->flash('message', 'Produto salvo com sucesso!');
            return redirect()->to(route('products.index'));

        } catch (\Illuminate\Validation\ValidationException $e) {
            $this->isSaving = false;
            $this->dispatch('form-failed');
            throw $e;
        } catch (\Exception $e) {
            $this->isSaving = false;
            $this->dispatch('form-failed');
            session()->flash('error', 'Erro ao salvar produto: ' . $e->getMessage());
        }
    }


    private function normalizePrice(string $price): float
    {
        $price = preg_replace('/[^\d,.]/', '', $price);
        if (strpos($price, ',') !== false) {
            $price = str_replace(',', '.', $price);
        }
        return (float) $price;
    }

    public function removePhoto($photoId)
    {
        if (!$photoId || !is_numeric($photoId)) {
            return;
        }

        if (!$this->product || !$this->product->exists) {
            session()->flash('error', 'Produto não encontrado.');
            return;
        }

        try {
            $image = $this->product->images()->findOrFail($photoId);

            if (Storage::disk('public')->exists($image->path)) {
                Storage::disk('public')->delete($image->path);
            }

            $image->delete();
            $this->existingPhotos = $this->product->images()->get();
            session()->flash('message', 'Foto removida com sucesso.');
        } catch (\Exception $e) {
            session()->flash('error', 'Erro ao remover foto: ' . $e->getMessage());
        }
    }

    public function openCropModal($index)
{
    $this->currentPhotoIndex = $index;
    $photo = $this->product->images[$index] ?? null;

    if ($photo) {
        $this->cropData = [
            'x' => $photo->crop_x ?? 0,
            'y' => $photo->crop_y ?? 0,
            'scale' => $photo->crop_scale ?? 1,
            'crop_data_x' => $photo->crop_data_x ?? 0,
            'crop_data_y' => $photo->crop_data_y ?? 0,
            'crop_data_width' => $photo->crop_data_width ?? 400,
            'crop_data_height' => $photo->crop_data_height ?? 400,
        ];
    }

    $this->showCropModal = true;

    // Se quiser enviar via dispatch para JS também:
    $this->dispatch('load-cropper', [
        'index' => $index,
        'photo' => $photo,
        'cropData' => $this->cropData,
    ]);
}

    public function closeCropModal()
    {
        $this->showCropModal = false;
        $this->currentPhotoIndex = 0;
    }

    public function saveCropData($cropData)
{
    $index = $cropData['index'] ?? $this->currentPhotoIndex;

    // Salva os dados do crop para uso posterior no método save()
    $this->cropData[$index] = [
        'x' => $cropData['x'] ?? 0,
        'y' => $cropData['y'] ?? 0,
        'scale' => $cropData['scale'] ?? 1,
        'crop_data_x' => $cropData['crop_data_x'] ?? 0,
        'crop_data_y' => $cropData['crop_data_y'] ?? 0,
        'crop_data_width' => $cropData['crop_data_width'] ?? 400,
        'crop_data_height' => $cropData['crop_data_height'] ?? 400,
    ];

    // Gera uma pré-visualização cortada para a imagem temporária
    try {
        $tempPhoto = $this->photos[$index];
        $imageManager = new ImageManager(new Driver());
        $img = $imageManager->read($tempPhoto->getRealPath());

        $w = (int) $this->cropData[$index]['crop_data_width'];
        $h = (int) $this->cropData[$index]['crop_data_height'];
        $x = (int) $this->cropData[$index]['crop_data_x'];
        $y = (int) $this->cropData[$index]['crop_data_y'];

        $img->crop($w, $h, $x, $y);

        // Converte a imagem cortada para um Data URL para a pré-visualização
        $encodedImage = (string) $img->encodeByExtension(
            $tempPhoto->getClientOriginalExtension() ?? 'jpg'
        );
        $dataUrl = 'data:' . $tempPhoto->getMimeType() . ';base64,' . base64_encode($encodedImage);

        // Armazena o Data URL para a view usar se o componente for atualizado
        $this->croppedPreviews[$index] = $dataUrl;

        // Dispara o evento para atualizar a UI imediatamente
        $this->dispatch('image-cropped', index: $index, url: $dataUrl);

    } catch (\Exception $e) {
        session()->flash('error', 'Erro ao gerar pré-visualização do corte: ' . $e->getMessage());
        Log::error('Erro ao gerar pré-visualização do corte', ['exception' => $e]);
    }

    $this->closeCropModal();
}

    public function saveExistingCropData($cropData)
{
    try {
        $photoIndex = $cropData['index'] ?? 0;
        $photo = $this->existingPhotos[$photoIndex] ?? null;

        if (!$photo) return;

        $photo->update([
            'crop_x' => $cropData['x'] ?? 0,
            'crop_y' => $cropData['y'] ?? 0,
            'crop_scale' => $cropData['scale'] ?? 1,
            'crop_data_x' => $cropData['crop_data_x'] ?? 0,
            'crop_data_y' => $cropData['crop_data_y'] ?? 0,
            'crop_data_width' => $cropData['crop_data_width'] ?? 400,
            'crop_data_height' => $cropData['crop_data_height'] ?? 400,
            'cache_version' => now()->timestamp,
        ]);

        $photo->generateCroppedImage();

        // Força a recarga da imagem no frontend com cache-busting
        $newImageUrl = Storage::url($photo->cropped_path) . '?v=' . $photo->cache_version;
        $this->dispatch('image-cropped', index: $photoIndex, url: $newImageUrl);

        session()->flash('message', 'Posição da imagem salva com sucesso!');
        $this->dispatch('$refresh'); // Mantém para atualizar outros dados se necessário
    } catch (\Exception $e) {
        session()->flash('error', 'Erro ao salvar posição da imagem: ' . $e->getMessage());
    }

    $this->closeCropModal();
}

    public function render()
    {
        return view('livewire.product-form-new');
    }
}
