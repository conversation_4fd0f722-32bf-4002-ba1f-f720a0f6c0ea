<?php

use App\Models\ProductImage;
use App\Livewire\ProductList;
use App\Livewire\ProductFormNew;
use App\Livewire\Settings\Profile;
use App\Livewire\ProductFormSimple;
use App\Livewire\Settings\Password;
use App\Livewire\Settings\Appearance;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\PdfController;
use App\Http\Controllers\ImageCropPreviewController;

Route::get('/', function () {
    //return view('welcome');
    return redirect()->route('login');
})->name('home');

use App\Http\Controllers\DebugImageController;

Route::get('debug/crop/{id}', [DebugImageController::class, 'show']);


Route::get('/imagem-preview/{id}', [ImageCropPreviewController::class, 'show'])->name('imagem.preview');

Route::view('/fallback', 'fallback')->name('fallback');

Route::get('/catalog/view', [PdfController::class, 'view'])->name('catalog.view');

Route::get('/catalog/paginated', [PdfController::class, 'paginated'])->name('catalog.paginated');

Route::view('dashboard', 'dashboard')
    ->middleware(['auth', 'verified'])
    ->name('dashboard');

Route::middleware(['auth'])->group(function () {
    Route::get('settings/profile', Profile::class)->name('settings.profile');
    Route::get('settings/password', Password::class)->name('settings.password');
    Route::get('settings/appearance', Appearance::class)->name('settings.appearance');

    // Rotas de produtos
    Route::get('/products', ProductList::class)->name('products.index');

    // Rotas com views específicas para garantir o layout
    Route::get('/products/create', function () {
        return view('products.create');
    })->name('products.create');

    Route::get('/products/{product}/edit', function (\App\Models\Product $product) {
        return view('products.edit', compact('product'));
    })->name('products.edit');

    // Rota de teste com componente simples
    Route::get('/products/test', ProductFormSimple::class)->name('products.test');
    // Rota alternativa para teste
    Route::get('/products/novo', ProductFormNew::class)->name('products.novo');

    // Rota para compartilhamento de produtos
    Route::get('/share-products', [\App\Http\Controllers\ShareController::class, 'index'])->name('share.products');
});

require __DIR__.'/auth.php';
