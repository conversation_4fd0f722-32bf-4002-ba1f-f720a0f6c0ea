<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
    <!-- Total de Produtos -->
    <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
        <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Total de Produtos</h3>
        <p class="mt-2 text-3xl font-bold text-gray-900 dark:text-white">{{ $totalProducts }}</p>
    </div>

    <!-- Valor do Estoque -->
    <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
        <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Valor do Estoque (Ativos)</h3>
        <p class="mt-2 text-3xl font-bold text-gray-900 dark:text-white">R$ {{ number_format($totalStockValue, 2, ',', '.') }}</p>
    </div>

    <!-- Produtos Ativos -->
    <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
        <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Produtos Ativos</h3>
        <p class="mt-2 text-3xl font-bold text-gray-900 dark:text-white">{{ $productsByStatus['ativo'] ?? 0 }}</p>
    </div>

    <!-- Produtos Vendidos -->
    <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
        <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Produtos Vendidos</h3>
        <p class="mt-2 text-3xl font-bold text-gray-900 dark:text-white">{{ $productsByStatus['vendido'] ?? 0 }}</p>
    </div>

    <!-- Últimos Produtos Adicionados -->
    <div class="md:col-span-2 lg:col-span-4 bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Últimos Produtos Adicionados</h3>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Produto</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Valor</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    @forelse ($latestProducts as $product)
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        @if($product->images->isNotEmpty())
                                            <img class="h-10 w-10 rounded-full object-cover" src="{{ Storage::url($product->images->first()->cropped_path ?? $product->images->first()->path) }}?v={{ $product->images->first()->cache_version }}" alt="">
                                        @endif
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900 dark:text-white">{{ $product->marca }}</div>
                                        <div class="text-sm text-gray-500 dark:text-gray-400">{{ $product->categoria }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">R$ {{ number_format($product->valor, 2, ',', '.') }}</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                    @switch($product->status)
                                        @case('ativo') bg-green-100 text-green-800 @break
                                        @case('inativo') bg-gray-100 text-gray-800 @break
                                        @case('vendido') bg-blue-100 text-blue-800 @break
                                        @case('reservado') bg-yellow-100 text-yellow-800 @break
                                    @endswitch
                                ">
                                    {{ ucfirst($product->status) }}
                                </span>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="3" class="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-500">
                                Nenhum produto encontrado.
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
</div>
