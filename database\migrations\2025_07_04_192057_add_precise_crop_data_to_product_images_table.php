<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('product_images', function (Blueprint $table) {
            $table->integer('crop_data_x')->nullable()->after('crop_scale');
            $table->integer('crop_data_y')->nullable()->after('crop_data_x');
            $table->integer('crop_data_width')->nullable()->after('crop_data_y');
            $table->integer('crop_data_height')->nullable()->after('crop_data_width');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('product_images', function (Blueprint $table) {
            $table->dropColumn([
                'crop_data_x',
                'crop_data_y',
                'crop_data_width',
                'crop_data_height',
            ]);
        });
    }
};
