@foreach($products as $produto)
  <div class="page-container">
    <div class="product-row">
      <div class="product-image-cell">
        @php
          $img = collect($produto['images'])->firstWhere('cropped_path') ?? $produto['images'][0] ?? null;
          $src = $img ? '/storage/' . ($img['cropped_path'] ?? $img['path']) : '';
        @endphp
        @if($src)
          <img src="{{ $src }}" class="cropped-image" alt="">
        @endif
      </div>
      <div class="product-details-cell">
        <p style="font:700 30px sans-serif; margin-bottom:18px">{{ $produto['marca'] }}</p>
        <p>{{ $produto['descricao'] }}</p>
        <p>{{ $produto['cor'] }}</p>
        <p>TAMANHO: {{ $produto['tamanho'] }}</p>
        <p>{{ $produto['caracteristicas'] }}</p>
        <p>{{ $produto['estado'] }}</p>
        <p style="margin-top:15px; font:700 36px sans-serif; color:red">
          R$ {{ number_format($produto['valor'], 2, ',', '.') }}
        </p>
      </div>
    </div>
  </div>
@endforeach