<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <title>Debug de Imagem</title>
    <style>
        body {
            font-family: sans-serif;
            padding: 20px;
        }
        .container {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }
        .card {
            flex: 1 1 30%;
            border: 1px solid #ccc;
            padding: 10px;
        }
        .image-wrapper {
            position: relative;
            display: inline-block;
            width: 100%;
        }
        .image-wrapper img {
            max-width: 100%;
            height: auto;
            display: block;
        }
        .overlay {
            position: absolute;
            border: 2px dashed red;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <h1>Debug de Crop - Imagem #{{ $image->id }}</h1>

    <div class="container">
        {{-- ORIGINAL COM OVERLAY --}}
        <div class="card">
            <h3>Imagem original com área de corte</h3>
            <div class="image-wrapper" id="original-container">
                <img id="original-image" src="{{ asset('storage/' . $image->path) }}" alt="Imagem original">
                <div id="overlay" class="overlay"></div>
            </div>
        </div>

        {{-- PREVIEW RAW (crop exato antes do resize) --}}
        <div class="card">
            <h3>Preview do Corte Exato (sem resize)</h3>
            <img src="{{ asset('storage/products/debug/' . $image->id . '_preview.jpg') }}" alt="Preview">
        </div>

        {{-- IMAGEM FINAL 400x400 --}}
        <div class="card">
            <h3>Imagem final salva (400x400)</h3>
            <img src="{{ asset('storage/' . $image->cropped_path) }}" alt="Final">
        </div>
    </div>

    <script>
        window.addEventListener('load', function () {
            const overlay = document.getElementById('overlay');
            const originalImage = document.getElementById('original-image');

            const cropX = {{ $image->crop_data_x }};
            const cropY = {{ $image->crop_data_y }};
            const cropW = {{ $image->crop_data_width }};
            const cropH = {{ $image->crop_data_height }};

            const naturalW = originalImage.naturalWidth;
            const naturalH = originalImage.naturalHeight;

            const container = document.getElementById('original-container');
            const rect = container.getBoundingClientRect();
            const scaleX = rect.width / naturalW;
            const scaleY = rect.height / naturalH;

            overlay.style.left = (cropX * scaleX) + 'px';
            overlay.style.top = (cropY * scaleY) + 'px';
            overlay.style.width = (cropW * scaleX) + 'px';
            overlay.style.height = (cropH * scaleY) + 'px';
        });
    </script>
</body>
</html>
