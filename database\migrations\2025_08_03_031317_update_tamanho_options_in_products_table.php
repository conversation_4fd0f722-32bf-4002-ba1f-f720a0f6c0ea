<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Definir a nova lista de tamanhos
        $newSizes = ['PP', 'P', 'M', 'G', 'GG', 'G1', 'Único', '34', '36', '38', '40', '42', '44', '46', '48', '50'];

        // Alterar a coluna para usar a nova lista de ENUM.
        // Como estamos apenas adicionando valores e não renomeando,
        // uma alteração direta é geralmente segura em MySQL, mas a abordagem VARCHAR é mais robusta.
        Schema::table('products', function (Blueprint $table) {
            $table->string('tamanho', 255)->change();
        });

        Schema::table('products', function (Blueprint $table) use ($newSizes) {
            $table->enum('tamanho', $newSizes)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Definir a lista de tamanhos original
        $oldSizes = ['PP', 'P', 'M', 'G', 'G1', '34', '36', '38', '40', '42', '44', '46', '48', '50'];

        // Alterar a coluna de volta para a lista original
        Schema::table('products', function (Blueprint $table) {
            $table->string('tamanho', 255)->change();
        });

        Schema::table('products', function (Blueprint $table) use ($oldSizes) {
            $table->enum('tamanho', $oldSizes)->change();
        });
    }
};
