<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("ALTER TABLE products MODIFY COLUMN categoria ENUM(
            'Cal<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Casacos / Jaquetas', '<PERSON><PERSON>', 'Conjun<PERSON>', 'Lingerie', '<PERSON>ac<PERSON><PERSON>', 'Pijamas',
            'Regatas / Croppeds', '<PERSON><PERSON>', 'Shorts', '<PERSON>ênis / Sapatos', 'T-shirts / Camisetas', 'Vestido<PERSON>',
            'Bijuterias', '<PERSON>ls<PERSON>', '<PERSON>p<PERSON>us / Bon<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Relógios'
        ) NULL");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::statement("ALTER TABLE products MODIFY COLUMN categoria ENUM(
            'Camisas', 'Casacos / Jaquetas', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Ling<PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>',
            '<PERSON><PERSON><PERSON> / Croppeds', '<PERSON><PERSON>', '<PERSON>s', '<PERSON><PERSON><PERSON> / <PERSON>', 'T-shirts / Camisetas', '<PERSON>est<PERSON><PERSON>',
            '<PERSON><PERSON>ju<PERSON><PERSON>', '<PERSON>lsas', '<PERSON>péus / Bonés', 'Cintos', 'Óculos', 'Relógios'
        ) NULL");
    }
};
