<div class="p-4 sm:p-6 lg:p-8">
    <div class="mx-auto max-w-4xl">
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white sm:text-3xl">
            {{ $isEditMode ? 'Editar Produto' : 'Cadastro de Produto' }}
        </h1>

        <p class="mt-4 text-gray-500 dark:text-gray-400">
            {{ $isEditMode ? 'Altere os campos abaixo para editar o produto.' : 'Preencha os campos abaixo para cadastrar um novo produto.' }}
        </p>
    </div>

    <form wire:submit.prevent="save" class="mx-auto mb-0 mt-8 max-w-4xl space-y-4">
        @if (session()->has('message'))
            <div class="rounded-lg border border-success-300 bg-success-50 p-4 text-success-700" role="alert">
                <div class="flex items-center gap-4">
                    <span class="p-2 text-white bg-success-600 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </span>
                    <p class="font-medium text-sm">{{ session('message') }}</p>
                </div>
            </div>
        @endif

        <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 mt-4">
            <div>
                <label for="marca" class="block text-sm font-medium text-gray-700 dark:text-gray-200">Marca</label>
                <input type="text" id="marca" wire:model.lazy="marca" class="mt-1 w-full rounded-md border-gray-200 bg-white text-sm text-gray-700 shadow-sm dark:border-gray-700 dark:bg-gray-800 dark:text-gray-200 px-3 py-3">
                @error('marca') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
            </div>

            <div>
                <label for="cor" class="block text-sm font-medium text-gray-700 dark:text-gray-200">Cor</label>
                <input type="text" id="cor" wire:model.lazy="cor" class="mt-1 w-full rounded-md border-gray-200 bg-white text-sm text-gray-700 shadow-sm dark:border-gray-700 dark:bg-gray-800 dark:text-gray-200 px-3 py-3">
                @error('cor') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
            </div>
        </div>

        <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 mt-4">
            <div>
                <label for="tamanho" class="block text-sm font-medium text-gray-700 dark:text-gray-200">Tamanho</label>
                <select id="tamanho" wire:model="tamanho" class="mt-1 w-full rounded-md border-gray-200 bg-white text-sm text-gray-700 shadow-sm dark:border-gray-700 dark:bg-gray-800 dark:text-gray-200 px-3 py-3">
                    <option value="PP">PP</option>
                    <option value="P">P</option>
                    <option value="M">M</option>
                    <option value="G">G</option>
                    <option value="G1">G1</option>
                </select>
                @error('tamanho') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
            </div>

            <div>
                <label for="estado" class="block text-sm font-medium text-gray-700 dark:text-gray-200">Estado</label>
                <select id="estado" wire:model="estado" class="mt-1 w-full rounded-md border-gray-200 bg-white text-sm text-gray-700 shadow-sm dark:border-gray-700 dark:bg-gray-800 dark:text-gray-200 px-3 py-3">
                    <option value="Nova">Nova</option>
                    <option value="Seminova">Seminova</option>
                    <option value="Usada">Usada</option>
                </select>
                @error('estado') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
            </div>
        </div>

        <div class="mt-4">
            <label for="caracteristicas" class="block text-sm font-medium text-gray-700 dark:text-gray-200">Características</label>
            <textarea id="caracteristicas" wire:model.lazy="caracteristicas" rows="4" class="mt-1 w-full rounded-md border-gray-200 bg-white text-sm text-gray-700 shadow-sm dark:border-gray-700 dark:bg-gray-800 dark:text-gray-200 px-3 py-3"></textarea>
            @error('caracteristicas') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
        </div>

        <div class="mt-4">
            <label for="descricao" class="block text-sm font-medium text-gray-700 dark:text-gray-200">Descrição</label>
            <textarea id="descricao" wire:model.lazy="descricao" rows="4" class="mt-1 w-full rounded-md border-gray-200 bg-white text-sm text-gray-700 shadow-sm dark:border-gray-700 dark:bg-gray-800 dark:text-gray-200 px-3 py-3"></textarea>
            @error('descricao') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
        </div>

        <div class="mt-4">
            <label for="valor" class="block text-sm font-medium text-gray-700 dark:text-gray-200">Valor (R$)</label>
            <input type="text" id="valor" wire:model.lazy="valor" placeholder="Ex: 99,90" class="mt-1 w-full rounded-md border-gray-200 bg-white text-sm text-gray-700 shadow-sm dark:border-gray-700 dark:bg-gray-800 dark:text-gray-200 px-3 py-3">
            @error('valor') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
        </div>

        <div class="mt-4">
            <label for="photos" class="block text-sm font-medium text-gray-700 dark:text-gray-200">Fotos</label>
            <input type="file" id="photos" wire:model="photos" multiple class="mt-1 w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
            @error('photos.*') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
        </div>

        <div wire:loading wire:target="photos" class="text-sm text-gray-500">
            Carregando imagens...
        </div>

        @if ($isEditMode && $existingPhotos->isNotEmpty())
            <div class="mt-4">
                <p class="block text-sm font-medium text-gray-700 dark:text-gray-200">Fotos existentes</p>
                <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4 mt-2">
                    @foreach ($existingPhotos as $photo)
                        <div class="relative">
                            <img src="{{ Storage::url($photo->path) }}" class="object-cover w-full h-32 rounded-lg">
                            <button wire:click.prevent="removePhoto({{ $photo->id }})" class="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 text-xs">
                                &#x2715;
                            </button>
                        </div>
                    @endforeach
                </div>
            </div>
        @endif

        @if ($photos)
            <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                @foreach ($photos as $photo)
                    <img src="{{ $photo->temporaryUrl() }}" class="object-cover w-full h-32 rounded-lg">
                @endforeach
            </div>
        @endif

        <div class="flex items-center justify-end">
            <button type="submit" class="inline-block rounded-lg bg-blue-600 px-5 py-3 text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-4 focus:ring-blue-300 dark:focus:ring-blue-800">
                Salvar Produto
            </button>
        </div>
    </form>
</div>
