<?php

namespace App\Models;

use Illuminate\Support\Facades\Log;
use Intervention\Image\ImageManager;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\Drivers\Gd\Driver;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ProductImage extends Model
{
    use HasFactory;

    protected $fillable = [
        'product_id', 'path', 'cropped_path',
        'crop_x', 'crop_y', 'crop_scale',
        'crop_data_x', 'crop_data_y', 'crop_data_width', 'crop_data_height',
        'cache_version'
    ];

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    protected static function booted(): void
    {
        static::deleting(function (ProductImage $image) {
            // Excluir os arquivos de imagem do armazenamento
            Storage::disk('public')->delete(array_filter([$image->path, $image->cropped_path]));
            Log::info('Imagens excluídas do armazenamento', ['original' => $image->path, 'cropped' => $image->cropped_path]);
        });
    }

   public function generateCroppedImage(): void
{
    /* ---------- original ---------- */
    $origPath = storage_path("app/public/{$this->path}");
    if (!is_file($origPath)) {
        Log::error('[CROP] original não encontrada', ['path'=>$origPath]);
        return;
    }

    $img = (new ImageManager(new Driver()))->read($origPath);

    /* ---------- coords vindas do JS ---------- */
    $x = (int) $this->crop_data_x;
    $y = (int) $this->crop_data_y;
    $w = (int) $this->crop_data_width;
    $h = (int) $this->crop_data_height;

    /* limita aos limites da imagem */
    $w = max(1, min($w, $img->width()  - $x));
    $h = max(1, min($h, $img->height() - $y));

    /* ----------------------------------------------------------------
       1. GARANTE recorte quadrado - lado = menor dimensão  ------------
    -----------------------------------------------------------------*/
    $side = max($w, $h);          // novo lado
    $cropX = $x + ($w - $side) / 2;   // centraliza dentro do rect recebido
    $cropY = $y + ($h - $side) / 2;

    Log::info('[CROP] recorte final', [
        'natural'=>[$img->width(), $img->height()],
        'crop'   =>[$cropX, $cropY, $side, $side]
    ]);

    $crop = $img->crop($side, $side, $cropX, $cropY);

    /* ----------------------------------------------------------------
       2. REDIMENSIONA exatamente para 400 × 400 (mantém proporção) ----
    -----------------------------------------------------------------*/
    $crop->resize(400, 400, fn($c) => $c->aspectRatio());

    /* se por acaso o lado continuar < 400 (imagem pequenina) -------- */
    if ($crop->width() < 400 || $crop->height() < 400) {
        $canvas = (new ImageManager(new Driver()))
                  ->create(400, 400)->fill('ffffff');
        $canvas->place(
            $crop,
            'top-left',
            (400 - $crop->width())  / 2,
            (400 - $crop->height()) / 2
        );
        $out = $canvas;
    } else {
        $out = $crop;   // já está 400 × 400
    }

    /* ---------- salva ---------- */
    $ext   = strtolower(pathinfo($this->path, PATHINFO_EXTENSION));
    $mime  = $ext === 'png' ? 'png' : ($ext === 'webp' ? 'webp' : 'jpeg');
    $dest  = "products/cropped/{$this->id}.{$ext}";

    // Garante que o diretório de destino exista
    Storage::disk('public')->makeDirectory('products/cropped');

    $out->save(storage_path("app/public/{$dest}"), 90, $mime);

    $this->update(['cropped_path' => $dest]);

    Log::info('[CROP] salvo', ['path'=>$dest]);
}




    /* ---------------------------------------------------------------------
       REMOVE IMAGENS ASSOCIADAS (Este método não é mais necessário, pois a lógica foi movida para o evento 'deleting')                                           
    ---------------------------------------------------------------------*/
    // public function deleteWithFiles(): void
    // {
    //     Storage::disk('public')->delete(array_filter([$this->path, $this->cropped_path]));
    //     $this->delete();
    // }

}
