<?php

namespace App\Http\Controllers;

use App\Models\ProductImage;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;

class ImageCropPreviewController extends Controller
{
    public function show($id)
    {
        $image = ProductImage::findOrFail($id);

        if (!$image->path || !Storage::disk('public')->exists($image->path)) {
            abort(404, 'Imagem original não encontrada.');
        }

        $manager = new ImageManager(new Driver());

        try {
            $imageContent = Storage::disk('public')->get($image->path);
            $img = $manager->read($imageContent);

            // 📌 Tamanho final da imagem cortada
            $finalSize = 400;

            if (
                isset($image->crop_data_width) && $image->crop_data_width > 0 &&
                isset($image->crop_data_height) && $image->crop_data_height > 0 &&
                isset($image->crop_data_x) && isset($image->crop_data_y) &&
                1==2
            ) {
                // ✅ Crop com dados absolutos
                $img->crop(
                    (int) $image->crop_data_width,
                    (int) $image->crop_data_height,
                    (int) $image->crop_data_x,
                    (int) $image->crop_data_y
                );

                $img->scaleDown($finalSize, $finalSize);
            } else {
                // 🔁 Fallback baseado em CSS (translate + scale)
                $scale = $image->crop_scale ?? 1;
                $x = $image->crop_x ?? 0;
                $y = $image->crop_y ?? 0;

                $newWidth = (int) round($img->width() * $scale);
                $newHeight = (int) round($img->height() * $scale);

                //$newWidth = (int) round(400*1.75);
                //$newHeight = (int) round(446*1.75);

                $img = $img->scaleDown($newWidth, $newHeight);

                $adjustedX = (int) round($x * $scale *(-1));
                $adjustedY = (int) round($y * $scale *(-1));

                // Cria canvas 200x200 com fundo branco
                //$canvas = $manager->create($finalSize, $finalSize)->fill('ffffff');
                //$canvas->place($img, 'top-left', $adjustedX, $adjustedY);
                //$img = $canvas;

                $img->crop($finalSize, $finalSize, $adjustedX, $adjustedY);
            }

            return response($img->toPng()->toString(), 200, [
                'Content-Type' => 'image/png',
                'Cache-Control' => 'no-cache, no-store, must-revalidate',
            ]);

        } catch (\Exception $e) {
            Log::error('Erro ao gerar imagem crop preview: ' . $e->getMessage(), [
                'image_id' => $id,
                'path' => $image->path,
            ]);
            abort(500, 'Erro ao processar imagem.');
        }
    }
}
