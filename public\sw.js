const staticCacheName = 'site-static-v5'; // Versão atualizada para forçar cache invalidation
const dynamicCacheName = 'site-dynamic-v2';
const assets = [
    '/',
    '/fallback' // Adiciona a página de fallback ao cache estático
    // Removendo assets com hash para evitar falhas no addAll durante a instalação
    // Eles serão cacheados dinamicamente no evento fetch
];

// install event
self.addEventListener('install', evt => {
    // Skip waiting to activate the new service worker as soon as possible
    self.skipWaiting();
    evt.waitUntil(
        caches.open(staticCacheName).then((cache) => {
            console.log('caching shell assets');
            return cache.addAll(assets);
        })
    );
});

// activate event
self.addEventListener('activate', evt => {
    evt.waitUntil(
        caches.keys().then(keys => {
            return Promise.all(keys
                .filter(key => key !== staticCacheName && key !== dynamicCacheName)
                .map(key => caches.delete(key))
            );
        })
    );
});

// fetch event
self.addEventListener('fetch', evt => {
    const { request } = evt;
    const url = new URL(request.url);

    console.log(`[Service Worker] Fetching: ${request.method} ${request.url}`);


    // Ignora requisições de extensões do Chrome ou que não sejam http/https
    if (!url.protocol.startsWith('http')) {
        return;
    }

    // Lógica para requisições que não devem ser cacheáveis ou que precisam de tratamento especial
    if (request.method === 'POST') {
        // Para requisições POST, não use o cache. Vá direto para a rede.
        // Isso evita o problema de envio duplicado.
        evt.respondWith(fetch(request));
        return; // Importante: para a execução aqui.
    }

    // Ignora requisições de autenticação e de imagens de produtos
    if (['/login', '/register', '/logout'].some(p => url.pathname.startsWith(p)) || url.pathname.includes('/storage/products/')) {
        // Para essas, podemos usar Cache First ou apenas fetch, dependendo da necessidade.
        // Por enquanto, vamos apenas fazer fetch para evitar problemas de cache.
        return fetch(request);
    }

    // Estratégia Network First para o catálogo e a lista de produtos
    if (url.pathname === '/catalog/view' || url.pathname === '/products' || url.pathname === '/dashboard') {
        evt.respondWith(
            fetch(request)
                .then(fetchRes => {

                    // Se a rede funcionar, clona a resposta e guarda no cache dinâmico
                    const cacheCopy = fetchRes.clone();
                    caches.open(dynamicCacheName).then(cache => {
                        cache.put(request.url, cacheCopy);
                    });
                    return fetchRes;
                })
                .catch(() => {
                    // Se a rede falhar, tenta pegar do cache
                    return caches.match(request.url).then(cacheRes => {
                        // Se não tiver no cache, usa a página de fallback
                        return cacheRes || caches.match('/fallback');
                    });
                })
        );
        return;
    }

    // Para páginas HTML, usa a estratégia Stale-While-Revalidate
    if (request.headers.get('accept').includes('text/html')) {
        evt.respondWith(
            caches.open(dynamicCacheName).then(cache => {
                return cache.match(request).then(response => {
                    // Faz a requisição à rede em paralelo
                    let fetchPromise = fetch(request).then(networkResponse => {
                        // Se a requisição for bem-sucedida, atualiza o cache
                        cache.put(request, networkResponse.clone());
                        return networkResponse;
                    });

                    // Retorna a resposta do cache imediatamente (se existir),
                    // ou espera a resposta da rede (se não houver nada no cache)
                    return response || fetchPromise;
                }).catch(() => {
                    // Se tudo falhar (sem cache, sem rede), mostra a página de fallback
                    return caches.match('/fallback');
                })
            })
        );
        return;
    }

    // Para outros assets (CSS, JS, imagens), usa a estratégia Stale-While-Revalidate com fallback
    evt.respondWith(
        caches.open(dynamicCacheName).then(cache => {
            return cache.match(request).then(response => {
                // Faz a requisição à rede em paralelo
                let fetchPromise = fetch(request).then(networkResponse => {
                    // Se a requisição for bem-sucedida, atualiza o cache
                    cache.put(request, networkResponse.clone());
                    return networkResponse;
                });

                // Retorna a resposta do cache imediatamente (se existir),
                // ou espera a resposta da rede (se não houver nada no cache)
                return response || fetchPromise;
            }).catch(() => {
                // Se tudo falhar (sem cache, sem rede), mostra a página de fallback
                return caches.match('/fallback');
            })
        })
    );
});
