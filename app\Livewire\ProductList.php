<?php

namespace App\Livewire;

use App\Models\Product;
use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Facades\Storage;

class ProductList extends Component
{
    use WithPagination;

    public function updateStatus($productId, $status)
    {
        try {
            $product = Product::findOrFail($productId);
            $product->status = $status;
            $product->save();
            $message = 'Status do produto atualizado com sucesso.';

            // Despacha um evento para o Alpine.js com a mensagem
            $this->dispatch('status-updated', message: $message);

        } catch (\Exception $e) {
            session()->flash('error', 'Erro ao atualizar o status do produto: ' . $e->getMessage());
        }
    }

    public function deleteProduct($productId)
    {
        try {
            $product = Product::findOrFail($productId);

            // Remove imagens associadas
            foreach ($product->images as $image) {
                // Verifica se o arquivo existe antes de tentar deletar
                if (Storage::disk('public')->exists($image->path)) {
                    Storage::disk('public')->delete($image->path);
                }
                $image->delete();
            }

            $product->delete();
            $message = 'Produto excluído com sucesso.';
            $this->dispatch('status-updated', message: $message);
            // Redefine a página para a primeira página após a exclusão
            $this->resetPage();
        } catch (\Exception $e) {
            session()->flash('error', 'Erro ao excluir produto: ' . $e->getMessage());
        }
    }

    public function render()
    {
        // Otimização: carrega apenas a primeira imagem para listagem
        $products = Product::with(['images' => function ($query) {
            $query->orderBy('id')->limit(1);
        }])
        ->latest()
        ->paginate(10);

        return view('livewire.product-list', [
            'products' => $products,
        ]);
    }
}
