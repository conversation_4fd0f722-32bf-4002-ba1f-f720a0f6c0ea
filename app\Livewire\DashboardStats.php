<?php

namespace App\Livewire;

use App\Models\Product;
use Livewire\Component;
use Illuminate\Support\Facades\DB;

class DashboardStats extends Component
{
    public $totalProducts;
    public $totalStockValue;
    public $productsByStatus;
    public $latestProducts;

    public function mount()
    {
        $this->totalProducts = Product::count();
        $this->totalStockValue = Product::where('status', 'ativo')->sum('valor');
        
        $this->productsByStatus = Product::query()
            ->select('status', DB::raw('count(*) as total'))
            ->groupBy('status')
            ->get()
            ->pluck('total', 'status');

        $this->latestProducts = Product::with('images')
            ->latest()
            ->take(5)
            ->get();
    }

    public function render()
    {
        return view('livewire.dashboard-stats');
    }
}
