<x-layouts.app>
    <div x-data="shareProducts()" class="container mx-auto p-4">
        <h1 class="text-2xl font-bold mb-4">Compartilhar Produtos</h1>

        <div class="my-6 text-center">
            <button @click="share()" class="bg-blue-500 text-white px-6 py-2 rounded-lg" :disabled="selectedProducts.length === 0">
                Compartilhar Selecionados
            </button>
        </div>

        <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            @foreach($products as $product)
                <div class="border rounded-lg p-2 text-center">
                    <input type="checkbox" id="product_{{ $product->id }}" value="{{ $product->id }}" x-model="selectedProducts" class="mb-2">
                    <label for="product_{{ $product->id }}">
                        @if($product->images->isNotEmpty())
                            <img src="{{ Storage::url($product->images->first()->cropped_path ?? $product->images->first()->path) }}" alt="{{ $product->descricao }}" class="w-full h-32 object-cover mb-2">
                        @endif
                        <p class="text-sm">{{ $product->marca }}</p>
                    </label>
                </div>
            @endforeach
        </div>
    </div>

    <script>
        function shareProducts() {
            return {
                selectedProducts: [],
                products: @json($products->keyBy('id')),
                async share() {
                    if (navigator.share) {
                        const files = await this.getFilesForSelectedProducts();
                        navigator.share({
                            title: 'Confira nossas últimas novidades!',
                            text: 'Últimas novidades:',
                            url: '{{ route("catalog.view") }}',
                            files: files,
                        }).catch(console.error);
                    } else {
                        alert('A função de compartilhamento não é suportada neste navegador.');
                    }
                },
                async getFilesForSelectedProducts() {
                    const files = [];
                    for (const productId of this.selectedProducts) {
                        const product = this.products[productId];
                        if (product.images && product.images.length > 0) {
                            const imageUrl = '{{ Storage::url('') }}' + (product.images[0].cropped_path ?? product.images[0].path);
                            try {
                                const response = await fetch(imageUrl);
                                const blob = await response.blob();
                                const file = new File([blob], product.images[0].path.split('/').pop(), { type: blob.type });
                                files.push(file);
                            } catch (error) {
                                console.error('Erro ao buscar imagem:', error);
                            }
                        }
                    }
                    return files;
                }
            }
        }
    </script>
</x-layouts.app>
