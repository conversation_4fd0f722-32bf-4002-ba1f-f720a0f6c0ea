@extends('layouts.app')

@section('content')
<div class="p-6 space-y-6">
    <h2 class="text-2xl font-bold">Debug de Crop da Imagem #{{ $image->id }}</h2>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        {{-- Imagem original com overlay --}}
        <div>
            <h3 class="text-lg font-semibold mb-2">Imagem Original com Crop Overlay</h3>
            <div class="relative border">
                <img src="{{ asset('storage/' . $image->path) }}"
                     alt="Original"
                     class="w-full h-auto">

                <div class="absolute border-2 border-red-500"
                     style="
                         left: {{ ($image->crop_data_x / $naturalWidth) * 100 }}%;
                         top: {{ ($image->crop_data_y / $naturalHeight) * 100 }}%;
                         width: {{ ($image->crop_data_width / $naturalWidth) * 100 }}%;
                         height: {{ ($image->crop_data_height / $naturalHeight) * 100 }}%;
                     ">
                </div>
            </div>
            <p class="text-sm mt-2 text-gray-500">
                Dimensões originais: {{ $naturalWidth }} x {{ $naturalHeight }}px
            </p>
        </div>

        {{-- Preview bruto cortado --}}
        <div>
            <h3 class="text-lg font-semibold mb-2">Preview Bruto do Corte</h3>
            <img src="{{ asset('storage/products/debug/' . $image->id . '_preview.jpg') }}" class="w-full h-auto border">
        </div>

        {{-- Imagem final 400x400 --}}
        <div>
            <h3 class="text-lg font-semibold mb-2">Imagem Final Salva (400x400)</h3>
            <img src="{{ asset('storage/' . $image->cropped_path) }}" class="w-full h-auto border">
        </div>
    </div>

    <div class="mt-6 p-4 bg-gray-100 text-sm rounded shadow">
        <h4 class="font-bold mb-1">Crop Data</h4>
        <ul class="space-y-1">
            <li><strong>X:</strong> {{ $image->crop_data_x }}</li>
            <li><strong>Y:</strong> {{ $image->crop_data_y }}</li>
            <li><strong>Largura:</strong> {{ $image->crop_data_width }}</li>
            <li><strong>Altura:</strong> {{ $image->crop_data_height }}</li>
        </ul>
    </div>
</div>
@endsection
