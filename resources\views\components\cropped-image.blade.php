@props([
    'image',
    'alt' => '',
    'class' => '',
    'containerClass' => '',
    'size' => 200,
])

@php
    use Illuminate\Support\Facades\Storage;

    $cropX = $image->crop_x ?? 0;
    $cropY = $image->crop_y ?? 0;
    $cropScale = $image->crop_scale ?? 1;
@endphp

<div class="relative overflow-hidden {{ $containerClass }}"
     style="width: {{ $size }}px; height: {{ $size }}px; background-color: #f3f4f6;">
    <img src="{{ Storage::url($image->cropped_path ?? $image->path) }}"
         alt="{{ $alt }}"
         class="absolute top-0 left-0 {{ $class }}"
         style="transform: translate({{ $cropX }}px, {{ $cropY }}px) scale({{ $cropScale }}); transform-origin: top left;"
         draggable="false" />
</div>
