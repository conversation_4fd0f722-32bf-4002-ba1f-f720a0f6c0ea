<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Product extends Model
{
    use HasFactory;

    protected $fillable = [
        'marca',
        'cor',
        'tamanho',
        'caracteristicas',
        'estado',
        'descricao',
        'valor',
        'status',
        'categoria',
    ];

    protected static function booted(): void
    {
        static::deleting(function (Product $product) {
            $product->images->each->delete();
        });
    }

    public function images(): HasMany
    {
        return $this->hasMany(ProductImage::class);
    }

    /**
     * Get the first image for performance optimization
     */
    public function firstImage()
    {
        return $this->hasOne(ProductImage::class)->orderBy('id');
    }

    /**
     * Get formatted price
     */
    public function getFormattedPriceAttribute(): string
    {
        return 'R$ ' . number_format($this->valor, 2, ',', '.');
    }
}
