## Laravel Pint Code Formatter

- You must run ___SINGLE_BACKTICK___vendor/bin/pint --dirty___SINGLE_BACKTICK___ before finalizing changes to ensure your code matches the project's expected style.
- Do not run ___SINGLE_BACKTICK___vendor/bin/pint --test___SINGLE_BACKTICK___, simply run ___SINGLE_BACKTICK___vendor/bin/pint___SINGLE_BACKTICK___ to fix any formatting issues.
<?php /**PATH C:\Users\<USER>\Herd\loja\storage\framework\views/964aa853df6b6323a02d40aed9a59b28.blade.php ENDPATH**/ ?>