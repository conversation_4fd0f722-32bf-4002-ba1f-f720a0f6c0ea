<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 1. Alterar temporariamente para VARCHAR para permitir a atualização
        Schema::table('products', function (Blueprint $table) {
            $table->string('categoria', 255)->change();
        });

        // 2. Atualizar os dados existentes para os novos valores
        DB::table('products')->where('categoria', 'Regatas / Croppeds')->update(['categoria' => 'Blusas / Croppeds']);
        DB::table('products')->where('categoria', 'T-shirts / Camisetas')->update(['categoria' => 'Regatas / T-shirts']);

        // 3. Alterar de volta para ENUM com a nova lista de categorias
        $newCategories = [
            'Calças', 'Cam<PERSON><PERSON>', 'Casacos / Jaquetas', '<PERSON><PERSON>', '<PERSON>jun<PERSON>', 'Lingerie', '<PERSON>acõ<PERSON>', 'Pijamas',
            'Blusas / Croppeds',
            'Saias', 'Shorts', 'Tênis / Sapatos',
            'Regatas / T-shirts',
            'Vestidos', 'Bijuterias', 'Bolsas', 'Chapéus / Bonés', 'Cintos', 'Óculos', 'Relógios'
        ];
        Schema::table('products', function (Blueprint $table) use ($newCategories) {
            $table->enum('categoria', $newCategories)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // 1. Alterar temporariamente para VARCHAR para permitir a atualização de volta
        Schema::table('products', function (Blueprint $table) {
            $table->string('categoria', 255)->change();
        });

        // 2. Reverter os dados para os valores antigos
        DB::table('products')->where('categoria', 'Blusas / Croppeds')->update(['categoria' => 'Regatas / Croppeds']);
        DB::table('products')->where('categoria', 'Regatas / T-shirts')->update(['categoria' => 'T-shirts / Camisetas']);

        // 3. Alterar de volta para ENUM com a lista original de categorias
        $oldCategories = [
            'Calças', 'Camisas', 'Casacos / Jaquetas', 'Coletes', 'Conjuntos', 'Lingerie', 'Macacões', 'Pijamas',
            'Regatas / Croppeds',
            'Saias', 'Shorts', 'Tênis / Sapatos',
            'T-shirts / Camisetas',
            'Vestidos', 'Bijuterias', 'Bolsas', 'Chapéus / Bonés', 'Cintos', 'Óculos', 'Relógios'
        ];
        Schema::table('products', function (Blueprint $table) use ($oldCategories) {
            $table->enum('categoria', $oldCategories)->change();
        });
    }
};
