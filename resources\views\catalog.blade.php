<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width,initial-scale=1.0">
  <title>Catálogo</title>

  {{-- Tailwind só no Web --}}
  @unless($isPdf)
    @vite(['resources/css/app.css','resources/js/app.js'])
  @endunless

  <style>
    @page { margin:0 }
    body {
      font-family:"Helvetica Neue",Helvetica,Arial,sans-serif;
      background:#f3eee8; color:#333; margin:0;
    }
    .page-break { page-break-after:always }
    .page-frame { position:absolute; inset:20px; border:10px solid #fff; box-sizing:border-box }
    .page-container       { display:table; width:100%; height:100vh; padding:30px; box-sizing:border-box }
    .product-row          { display:table-row }
    .product-row-content-wrapper { position:relative; display:flex; align-items:center; width:100% }
    .product-image-cell   { width:50%; text-align:center; padding:30px 15px; box-sizing:border-box }
    .product-details-cell { width:50%; padding:30px 15px; box-sizing:border-box }
    .cropped-image {  width: 400px; height: 400px; object-fit: cover; display: block; contain: paint; will-change: transform; backface-visibility: hidden; }

    @media(max-width:768px){
      .page-container       { display:block; height:auto; padding:15px }
      .product-row          { display:block; margin-bottom:30px }
      .product-row-content-wrapper { flex-direction:column }
      .web-product-item     { flex-direction:column; padding: 15px; }
      .product-image-cell,
      .product-details-cell {
        display:block; width:100%; padding:15px 0; text-align:center;
      }
      .product-details-cell { border-top:1px solid #ddd }
      .cropped-image        { width:100%; height:auto }
    }

    img.cropped-image {
      transform: translateZ(0);
      backface-visibility: hidden;
      contain: layout paint;
    }
    .reserved-overlay {
      position:absolute; inset:0; background:rgba(0,0,0,0.5);
      display:flex; align-items:center; justify-content:center;
      font-size:44px; font-weight:700; color:#fff; text-transform:uppercase;
    }

    /* Web-only styles */
    @media screen {
      .web-product-item {
        position: relative;
        display: flex;
        align-items: center;
        padding: 30px;
        box-sizing: border-box;
        border-bottom: 1px solid #ddd;
      }
    }
  </style>
</head>

<body>
  {{-- ===== WEB: botões fixos ===== --}}
  @unless($isPdf)
    @auth
    <a href="{{ route('products.index') }}" style="
      position:fixed; top:20px; left:20px; z-index:1000;
      background:rgba(255,255,255,0.7); padding:8px; border-radius:50%;
      font-size:24px; text-decoration:none; color:#333;
    ">←</a>
    @endauth

    {{-- Painel flutuante com formulário de filtros --}}
<div style="position:fixed; bottom:20px; right:20px; z-index:1000; display:flex; flex-direction: column; gap:10px; align-items:flex-end">
  {{-- Botão para abrir/fechar painel --}}
  <button onclick="toggleFilter()" class="bg-blue-600 hover:bg-blue-700 text-white px-5 py-3 rounded-full">
    Filtrar
  </button>

  {{-- Painel flutuante --}}
  <div id="filter-panel" style="display:none"
  class="bg-white p-4 rounded-lg shadow-lg border w-64"
>
  <div style="max-height:360px; overflow-y:auto">
    <form method="GET" action="{{ route('catalog.view') }}" id="filter-form">
      <h3 class="font-bold mb-2">Categorias</h3>
      @foreach($categories as $category)
        <label class="flex items-center space-x-2">
          <input type="checkbox" name="categories[]"
            value="{{ $category }}"
            onchange="document.getElementById('filter-form').submit()"
            {{ in_array($category, request('categories', [])) ? 'checked' : '' }}>
          <span>{{ $category }}</span>
        </label>
      @endforeach

      <hr class="my-4">

      <h3 class="font-bold mb-2">Tamanhos</h3>
      @foreach($sizes as $size)
        <label class="flex items-center space-x-2">
          <input type="checkbox" name="sizes[]"
            value="{{ $size }}"
            onchange="document.getElementById('filter-form').submit()"
            {{ in_array($size, request('sizes', [])) ? 'checked' : '' }}>
          <span>{{ $size }}</span>
        </label>
      @endforeach

      <div class="mt-4">
        <a href="{{ route('catalog.view') }}" class="text-sm text-gray-500 hover:text-gray-700">Limpar filtros</a>
      </div>
    </form>
  </div>
</div>

  {{-- Botão de Compartilhar --}}
    <button onclick="compartilhar()" class="bg-green-600 hover:bg-green-700 text-white px-5 py-3 rounded-full">
      Compartilhar
    </button>

</div>
  @endunless

  {{-- ===== CAPA ===== --}}
  <div style="height:100vh; position:relative">
    <div class="page-frame"></div>
    <div style="
      position:absolute; top:50%; left:50%;
      transform:translate(-50%,-50%); text-align:center
    ">
      <img src="{{ $logoSrc }}" style="max-width:300px; margin-bottom:30px">
      <h1 style="font-size:{{ $isPdf?'28px':'38px' }}">Catálogo de Produtos</h1>
    </div>
  </div>
  <div class="page-break"></div>

  {{-- ===== PRODUTOS PDF ===== --}}
  @if($isPdf)
    @foreach($products as $categoria => $chunks)
      <div style="height:100vh; position:relative">
        <div class="page-frame"></div>
        <div style="
          position:absolute; top:50%; left:50%;
          transform:translate(-50%,-50%); text-align:center
        ">
          <h1 style="font-size:48px; text-transform:uppercase">{{ $categoria }}</h1>
        </div>
      </div>
      <div class="page-break"></div>

      @foreach($chunks as $chunk)
        <div style="position:relative; height:100vh">
          <table class="page-container"><tbody>
            @foreach($chunk as $produto)
              @php
                $img = $produto->images->firstWhere('cropped_path','!=',null)
                     ?? $produto->images->first();
                $src = public_path('storage/'.($img->cropped_path ?? $img->path));
              @endphp
              <tr class="product-row">
                <td colspan="2" style="padding:0">
                  <div class="product-row-content-wrapper">
                    <div class="product-image-cell">
                      <img src="{{ $src }}" class="cropped-image">
                    </div>
                    <div class="product-details-cell">
                      <p style="font:700 20px sans-serif; margin-bottom:12px">{{ $produto->marca }}</p>
                      <p>{{ $produto->descricao }}</p>
                      <p>{{ $produto->cor }}</p>
                      <p>TAMANHO: {{ $produto->tamanho }}</p>
                      <p>{{ $produto->caracteristicas }}</p>
                      <p>{{ $produto->estado }}</p>
                      <p style="margin-top:15px; font:700 26px sans-serif; color:red">
                        R$ {{ number_format($produto->valor,2,',','.') }}
                      </p>
                    </div>
                    @if($produto->status == 'reservado')
                      <div class="reserved-overlay">Reservado</div>
                    @endif
                  </div>
                </td>
              </tr>
            @endforeach
          </tbody></table>
        </div>
        @unless($loop->last)<div class="page-break"></div>@endunless
      @endforeach
      @unless($loop->last)<div class="page-break"></div>@endunless
    @endforeach

  {{-- ===== PRODUTOS WEB (agrupados e filtrados sem Alpine) ===== --}}
  @else
  <div id="product-container">
    {{-- Itens iniciais --}}
  </div>

  <div id="loader" class="text-center py-10 text-gray-500">Carregando...</div>

  <script>
    function initCatalog() {
      // Remove old listener to prevent duplicates
      if (window.catalogScrollHandler) {
        window.removeEventListener('scroll', window.catalogScrollHandler);
      }

      let page = 1;
      let loading = false;
      let finished = false;
      let currentCategory = null;
      const container = document.getElementById('product-container');
      const loader = document.getElementById('loader');

      // Clear previous content
      if(container) container.innerHTML = '';
      if(loader) loader.innerText = 'Carregando...';


      async function loadMore() {
        if (loading || finished) return;
        loading = true;
        if(loader) loader.style.display = 'block';

        const params = new URLSearchParams(window.location.search);
        params.set('page', page);
        params.set('_', new Date().getTime()); // Cache buster

        const res = await fetch(`{{ route('catalog.paginated') }}?` + params.toString());
        const data = await res.json();

        if (data.length === 0) {
          finished = true;
          if(loader) loader.innerText = 'Fim do catálogo';
          return;
        }

        data.forEach(prod => {
          if (prod.categoria !== currentCategory) {
            currentCategory = prod.categoria;
            const categoryHeader = `
              <div style="text-align:center; padding: 60px 20px 30px;">
                <h2 style="font-size:28px; text-transform:uppercase; font-weight:700; color:#555;">${currentCategory}</h2>
              </div>
            `;
            if(container) container.insertAdjacentHTML('beforeend', categoryHeader);
          }

          const img = prod.images.find(i => i.cropped_path) || (prod.images.length > 0 ? prod.images[0] : null);
          let src = '';
          if (img) {
            const path = img.cropped_path || img.path;
            const version = img.cache_version ? `?v=${img.cache_version}` : '';
            src = `/storage/${path}${version}`;
          }

          const html = `
            <div class="web-product-item">
              <div class="product-image-cell">
                ${src ? `<img src="${src}" class="cropped-image" alt="" loading="lazy" decoding="async" fetchpriority="low">` : ''}
              </div>
              <div class="product-details-cell">
                <p style="font:700 30px sans-serif; margin-bottom:18px">${prod.marca}</p>
                <p>${prod.descricao}</p>
                <p>${prod.cor}</p>
                <p>TAMANHO: ${prod.tamanho}</p>
                <p>${prod.caracteristicas}</p>
                <p>${prod.estado}</p>
                <p style="margin-top:15px; font:700 36px sans-serif; color:red">
                  R$ ${parseFloat(prod.valor).toFixed(2).replace('.', ',')}
                </p>
              </div>
              ${prod.status === 'reservado' ? '<div class="reserved-overlay">Reservado</div>' : ''}
            </div>
          `;
          if(container) container.insertAdjacentHTML('beforeend', html);
        });

        page++;
        loading = false;
      }

      window.catalogScrollHandler = function() {
        if ((window.innerHeight + window.scrollY + 100) >= document.body.offsetHeight) {
          loadMore();
        }
      };

      window.addEventListener('scroll', window.catalogScrollHandler);
      loadMore(); // Initial load
    }

    document.addEventListener('DOMContentLoaded', () => {
      if (document.getElementById('product-container')) {
        initCatalog();
      }
    });

    document.addEventListener('livewire:navigated', () => {
      if (document.getElementById('product-container')) {
        initCatalog();
      }
    });
  </script>
@endif

  {{-- Script de compartilhamento --}}
  <script>
    function compartilhar() {
      if (!navigator.share) return;
      navigator.share({
        title: "Catálogo de Produtos",
        text: "Confira nosso catálogo!",
        url: window.location.href
      }).catch(console.error);
    }
  </script>

  <script>
  function toggleFilter() {
    const panel = document.getElementById('filter-panel');
    panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
  }
</script>

<script>
  function toggleFilter() {
    const panel = document.getElementById('filter-panel');
    panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
  }

  // Fechar ao clicar fora
  document.addEventListener('click', function(event) {
    const panel = document.getElementById('filter-panel');
    const button = event.target.closest('button');

    // Se o clique for fora do painel e fora do botão de filtro
    if (panel && panel.style.display !== 'none' &&
        !panel.contains(event.target) &&
        !(button && button.textContent.includes('Filtrar'))
    ) {
      panel.style.display = 'none';
    }
  });
</script>
</body>
</html>
