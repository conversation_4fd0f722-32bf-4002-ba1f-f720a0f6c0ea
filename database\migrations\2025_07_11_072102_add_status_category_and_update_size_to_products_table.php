<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            // Adicionar as novas colunas 'status' e 'categoria'
            $table->enum('status', ['ativo', 'inativo', 'reservado', 'vendido'])->default('ativo')->after('valor');
            $table->enum('categoria', [
                'Camisas', 'Casacos / Jaquetas', 'Coletes', 'Conjuntos', 'Lingerie', 'Macacões', 'Pijamas',
                'Regatas / Croppeds', 'Saias', 'Shorts', 'Tênis / Sapatos', 'T-shirts / Camisetas', 'Vestidos',
                'Bijuterias', 'Bolsas', 'Chap<PERSON>us / Bon<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>'
            ])->nullable()->after('status');
        });

        // Modificar a coluna 'tamanho' para incluir os novos valores numéricos
        // É necessário fazer isso fora do Schema::table para alguns bancos de dados ou para evitar erros com ENUM
        // Primeiro, renomeie a coluna existente para evitar conflitos
        Schema::table('products', function (Blueprint $table) {
            $table->renameColumn('tamanho', 'tamanho_old');
        });

        // Em seguida, adicione a nova coluna 'tamanho' com os valores atualizados
        Schema::table('products', function (Blueprint $table) {
            $table->enum('tamanho', [
                'PP', 'P', 'M', 'G', 'G1',
                '34', '36', '38', '40', '42', '44', '46', '48', '50'
            ])->after('cor');
        });

        // Copie os dados da coluna antiga para a nova
        DB::statement('UPDATE products SET tamanho = tamanho_old');

        // Remova a coluna antiga
        Schema::table('products', function (Blueprint $table) {
            $table->dropColumn('tamanho_old');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            // Remover as colunas adicionadas
            $table->dropColumn('status');
            $table->dropColumn('categoria');
        });

        // Reverter a coluna 'tamanho' para o estado original (ENUM)
        // Primeiro, renomeie a coluna existente para evitar conflitos
        Schema::table('products', function (Blueprint $table) {
            $table->renameColumn('tamanho', 'tamanho_old');
        });

        // Em seguida, adicione a coluna 'tamanho' original
        Schema::table('products', function (Blueprint $table) {
            $table->enum('tamanho', ['PP', 'P', 'M', 'G', 'G1'])->after('cor');
        });

        // Copie os dados da coluna antiga para a nova (apenas os valores válidos)
        DB::statement("UPDATE products SET tamanho = tamanho_old WHERE tamanho_old IN ('PP', 'P', 'M', 'G', 'G1')");

        // Remova a coluna antiga
        Schema::table('products', function (Blueprint $table) {
            $table->dropColumn('tamanho_old');
        });
    }
};
