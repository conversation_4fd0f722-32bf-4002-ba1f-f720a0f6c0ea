<div>
    <div class="p-4 sm:p-6 lg:p-8">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white sm:text-3xl">
                    @if($isEditMode ?? false)
                        Editar Produto
                    @else
                        Cadastro de Produto
                    @endif
                </h1>
                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    @if($isEditMode ?? false)
                        Altere os campos abaixo para editar o produto.
                    @else
                        Preencha os campos abaixo para cadastrar um novo produto.
                    @endif
                </p>
            </div>

            <div class="mt-4 sm:mt-0 sm:ml-4">
                <a href="{{ route('products.index') }}"
                   @click.prevent="$dispatch('loading-start'); window.location.href = '{{ route('products.index') }}';"
                   class="inline-block w-full sm:w-auto rounded-lg bg-gray-600 px-5 py-3 text-sm font-medium text-white text-center">
                    Voltar à Lista
                </a>
            </div>
        </div>

        <form wire:submit.prevent="save" class="mt-8 space-y-6" x-data="{ isSubmitting: false }" @submit="isSubmitting = true" @form-failed.window="isSubmitting = false">
            <fieldset wire:loading.attr="disabled" wire:loading.class="opacity-50 cursor-not-allowed" wire:target="photos, isAnalyzing">
            @if (session()->has('message'))
                <div class="rounded-lg border border-green-300 bg-green-50 p-4 text-green-700" role="alert">
                    <div class="flex items-center gap-4">
                        <span class="p-2 text-white bg-green-600 rounded-full">
                            <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </span>
                        <p class="font-medium text-sm">{{ session('message') }}</p>
                    </div>
                </div>
            @endif

            @if (session()->has('error'))
                <div class="rounded-lg border border-red-300 bg-red-50 p-4 text-red-700" role="alert">
                    <div class="flex items-center gap-4">
                        <span class="p-2 text-white bg-red-600 rounded-full">
                            <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </span>
                        <p class="font-medium text-sm">{{ session('error') }}</p>
                    </div>
                </div>
            @endif

            <div>
                <label for="photos" class="block text-sm font-medium text-gray-700 dark:text-gray-200">Fotos</label>
                <input type="file" id="photos" wire:model="photos" multiple class="mt-1 w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                @error('photos.*') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
            </div>

            <div wire:loading wire:target="photos, isAnalyzing" class="flex items-center space-x-2 text-sm text-gray-500">
                <svg class="animate-spin h-5 w-5 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span>Analisando Imagens...</span>
            </div>

            @if (($isEditMode ?? false) && isset($existingPhotos) && $existingPhotos->isNotEmpty())
                <div class="mt-4">
                    <p class="block text-sm font-medium text-gray-700 dark:text-gray-200">Fotos existentes</p>
                    <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4 mt-2">
                        @foreach ($existingPhotos as $index => $photo)
                            <div class="relative group" data-existing-photo-index="{{ $index }}">
                                <div class="aspect-square rounded-lg border-2 border-gray-200">
                                    <img src="{{ Storage::url($photo->cropped_path ?? $photo->path) }}?v={{ $photo->cache_version }}" class="w-full h-full object-cover">
                                </div>
                                <button type="button"
                                        onclick="openCropModalExisting({{ $index }}, '{{ Storage::url($photo->path) }}', {
                                                    x: {{ $photo->crop_x ?? 0 }},
                                                    y: {{ $photo->crop_y ?? 0 }},
                                                    scale: {{ $photo->crop_scale ?? 1 }},
                                                    crop_data_x: {{ $photo->crop_data_x ?? 0 }},
                                                    crop_data_y: {{ $photo->crop_data_y ?? 0 }},
                                                    crop_data_width: {{ $photo->crop_data_width ?? 400 }},
                                                    crop_data_height: {{ $photo->crop_data_height ?? 400 }}
                                                })"
                                        class="absolute inset-0 bg-black bg-opacity-50 text-white rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2z" />
                                    </svg>
                                    <span class="ml-2">Ajustar</span>
                                </button>
                                <button type="button" wire:click.prevent="removePhoto({{ $photo->id }})" class="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 text-xs z-10">
                                    &#x2715;
                                </button>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif

            @if (isset($photos) && $photos)
                <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                    @foreach ($photos as $index => $photo)
                        <div class="relative group" data-photo-index="{{ $index }}">
                            <div class="aspect-square overflow-hidden rounded-lg border-2 border-gray-200">
                                <img src="{{ $croppedPreviews[$index] ?? $photo->temporaryUrl() }}" class="w-full h-full object-cover">
                            </div>
                            <button type="button"
                                    onclick="openCropModal({{ $index }})"
                                    class="absolute inset-0 bg-black bg-opacity-50 text-white rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                </svg>
                                <span class="ml-2">Ajustar</span>
                            </button>
                        </div>
                    @endforeach
                </div>
            @endif

            <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <div>
                    <label for="marca" class="block text-sm font-medium text-gray-700 dark:text-gray-200">Marca</label>
                    <input type="text" id="marca" wire:model="marca" wire:ignore class="mt-1 w-full rounded-md border-gray-200 bg-white text-sm text-gray-700 shadow-sm px-3 py-3" onkeydown="if(event.key==='Enter') event.preventDefault();">
                    @error('marca') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                </div>

                <div>
                    <label for="cor" class="block text-sm font-medium text-gray-700 dark:text-gray-200">Cor</label>
                    <input type="text" id="cor" wire:model="cor" wire:ignore class="mt-1 w-full rounded-md border-gray-200 bg-white text-sm text-gray-700 shadow-sm px-3 py-3" onkeydown="if(event.key==='Enter') event.preventDefault();">
                    @error('cor') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                </div>
            </div>

            <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <div>
                    <label for="tamanho" class="block text-sm font-medium text-gray-700 dark:text-gray-200">Tamanho</label>
                    <select id="tamanho" wire:model="tamanho" wire:ignore class="mt-1 w-full rounded-md border-gray-200 bg-white text-sm text-gray-700 shadow-sm px-3 py-3">
                        <option value="">Selecione</option>
                        <option value="PP">PP</option>
                        <option value="P">P</option>
                        <option value="M">M</option>
                        <option value="G">G</option>
                        <option value="GG">GG</option>
                        <option value="G1">G1</option>
                        <option value="Único">Único</option>
                        <option value="34">34</option>
                        <option value="36">36</option>
                        <option value="38">38</option>
                        <option value="40">40</option>
                        <option value="42">42</option>
                        <option value="44">44</option>
                        <option value="46">46</option>
                        <option value="48">48</option>
                        <option value="50">50</option>
                    </select>
                    @error('tamanho') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                </div>

                <div>
                    <label for="estado" class="block text-sm font-medium text-gray-700 dark:text-gray-200">Estado</label>
                    <select id="estado" wire:model="estado" wire:ignore class="mt-1 w-full rounded-md border-gray-200 bg-white text-sm text-gray-700 shadow-sm px-3 py-3">
                        <option value="">Selecione</option>
                        <option value="Nova(o) com Etiqueta">Nova(o) com Etiqueta</option>
                        <option value="Nova(o)">Nova(o)</option>
                        <option value="Seminova(o)">Seminova(o)</option>
                        <option value="Usada(o)">Usada(o)</option>
                    </select>
                    @error('estado') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                </div>
            </div>

            <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-200">Status</label>
                    <select id="status" wire:model="status" wire:ignore class="mt-1 w-full rounded-md border-gray-200 bg-white text-sm text-gray-700 shadow-sm px-3 py-3">
                        <option value="ativo">Ativo</option>
                        <option value="inativo">Inativo</option>
                        <option value="reservado">Reservado</option>
                        <option value="vendido">Vendido</option>
                    </select>
                    @error('status') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                </div>

                <div>
                    <label for="categoria" class="block text-sm font-medium text-gray-700 dark:text-gray-200">Categoria</label>
                    <select id="categoria" wire:model="categoria" wire:ignore class="mt-1 w-full rounded-md border-gray-200 bg-white text-sm text-gray-700 shadow-sm px-3 py-3">
                        <option value="">Selecione</option>
                        <option value="Calças">Calças</option>
                        <option value="Camisas">Camisas</option>
                        <option value="Casacos / Jaquetas">Casacos / Jaquetas</option>
                        <option value="Coletes">Coletes</option>
                        <option value="Conjuntos">Conjuntos</option>
                        <option value="Lingerie">Lingerie</option>
                        <option value="Macacões">Macacões</option>
                        <option value="Pijamas">Pijamas</option>
                        <option value="Blusas / Croppeds">Blusas / Croppeds</option>
                        <option value="Saias">Saias</option>
                        <option value="Shorts">Shorts</option>
                        <option value="Tênis / Sapatos">Tênis / Sapatos</option>
                        <option value="Regatas / T-shirts">Regatas / T-shirts</option>
                        <option value="Vestidos">Vestidos</option>
                        <option value="Bijuterias">Bijuterias</option>
                        <option value="Bolsas">Bolsas</option>
                        <option value="Chapéus / Bonés">Chapéus / Bonés</option>
                        <option value="Cintos">Cintos</option>
                        <option value="Óculos">Óculos</option>
                        <option value="Relógios">Relógios</option>
                    </select>
                    @error('categoria') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                </div>
            </div>

            <div>
                <label for="descricao" class="block text-sm font-medium text-gray-700 dark:text-gray-200">Descrição</label>
                <textarea id="descricao" wire:model="descricao" wire:ignore rows="4" class="mt-1 w-full rounded-md border-gray-200 bg-white text-sm text-gray-700 shadow-sm px-3 py-3"></textarea>
                @error('descricao') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
            </div>

            <div>
                <label for="caracteristicas" class="block text-sm font-medium text-gray-700 dark:text-gray-200">Tecido</label>
                <textarea id="caracteristicas" wire:model="caracteristicas" wire:ignore rows="4" class="mt-1 w-full rounded-md border-gray-200 bg-white text-sm text-gray-700 shadow-sm px-3 py-3"></textarea>
                @error('caracteristicas') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
            </div>

            <div>
                <label for="valor" class="block text-sm font-medium text-gray-700 dark:text-gray-200">Valor (R$)</label>
                <input type="text" id="valor" wire:model="valor" wire:ignore class="mt-1 w-full rounded-md border-gray-200 bg-white text-sm text-gray-700 shadow-sm px-3 py-3" onkeydown="if(event.key==='Enter') event.preventDefault();">
                @error('valor') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
            </div>

            <div class="flex items-center justify-end">
                <button
                    type="submit"
                    x-bind:disabled="isSubmitting"
                    class="relative inline-flex items-center rounded-lg bg-blue-600 px-5 py-3 text-sm font-medium text-white hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                    wire:loading.attr="disabled"
                    wire:loading.class="bg-blue-500"
                    wire:target="save"
                >
                    <!-- Spinner -->
                    <svg 
                    wire:loading 
                    wire:target="save"
                    class="animate-spin absolute left-3 h-5 w-5 text-white" 
                    xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                    >
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"/>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"/>
                    </svg>

                    <!-- Texto dinâmico -->
                    <span wire:loading.remove wire:target="save">
                    Salvar Produto
                    </span>
                    <span wire:loading wire:target="save">
                    Salvando…
                    </span>
                </button>
                </div>
            </fieldset>
        </form>
</div>

<!-- ▸ Modal de Crop (400 × 400 fixo) ------------------------------------ -->
<div id="crop-modal"
     class="fixed inset-0 bg-black/50 z-50 hidden"
     style="display:none;align-items:center;justify-content:center;"
     onclick="closeCropModal()">

  <div class="bg-white rounded-lg p-6 max-w-2xl w-full mx-4"
       onclick="event.stopPropagation()">

      <!-- Cabeçalho ----------------------------------------------------- -->
      <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-semibold text-gray-900">
              Ajustar imagem
          </h3>
          <button type="button" onclick="closeCropModal()"
                  class="text-gray-400 hover:text-gray-600">
              <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6"
                   viewBox="0 0 24 24" fill="none"
                   stroke="currentColor" stroke-width="2">
                  <path stroke-linecap="round" stroke-linejoin="round"
                        d="M6 18L18 6M6 6l12 12"/>
              </svg>
          </button>
      </div>

      <p class="text-sm text-gray-600 mb-3">
          Arraste para posicionar · use o slider para zoom.
      </p>

      <!-- Área de corte (400×400) -------------------------------------- -->
      <div class="flex justify-center mb-4">
          <div id="crop-container"
               class="border-2 border-gray-300 bg-gray-100 overflow-hidden rounded-lg"
               style="width:400px;height:400px;position:relative;">
              <img id="crop-image"
                   class="absolute top-0 left-0 select-none"
                   style="transform-origin:top left" draggable="false">
          </div>
      </div>

      <!-- Slider de zoom ------------------------------------------------ -->
      <div class="flex items-center justify-center space-x-3 mb-4">
          <span class="text-sm text-gray-600">Zoom:</span>
          <input id="zoom-slider" type="range" class="w-32"
                 oninput="updateImageScale(this.value)">
          <span id="zoom-value" class="text-sm text-gray-600">100 %</span>
      </div>

      <!-- Botões -------------------------------------------------------- -->
      <div class="flex justify-end space-x-3">
          <button type="button" onclick="closeCropModal()"
                  class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200">
              Cancelar
          </button>
          <button type="button" onclick="saveCropPosition()"
                  class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700">
              Salvar
          </button>
      </div>
  </div>
</div>

<link  href="https://unpkg.com/cropperjs@1.6.2/dist/cropper.min.css" rel="stylesheet">

<script src="https://unpkg.com/cropperjs@1.6.2/dist/cropper.min.js"></script>

<script>
/* ——— módulo único ——— */
if (!window.cropperModule) {
  window.cropperModule = true;

  const $ = q => document.querySelector(q);
  let cp  = null;       // Cropper
  let fit = 1;          // zoom mínimo (lado maior = 400)

  /* -------- cria / recria cropper ------------------------------- */
  function startCropper(saved){
    const img = $('#crop-image');
    cp && cp.destroy();

    //fit = Math.min(400/img.naturalWidth, 400/img.naturalHeight);
    fit = 400 / Math.max(img.naturalWidth, img.naturalHeight);
    if (fit > 1) fit = 1;      // nunca upscale

    cp = new Cropper(img,{
      aspectRatio      : 1,
      viewMode         : 0,    // NUNCA sai do container
      autoCropArea     : 1,    // crop-box = container 400×400
      dragMode         : 'move',
      cropBoxMovable   : false,
      cropBoxResizable : false,
      zoomOnWheel      : true,
      toggleDragModeOnDblclick:false,

      ready(){

        /* crop-box fixo 400×400 e invisível -------------------- */
        cp.setCropBoxData({ left:0, top:0, width:400, height:400 });

        const slider = $('#zoom-slider');
        slider.min = 1; slider.max = 4; slider.step = .01;

        if (saved && saved.scale){
          cp.zoomTo(saved.scale);
          cp.moveTo(saved.x, saved.y);
          const rel = saved.scale / fit;
          slider.value = rel.toFixed(2);
          labelZoom(rel);
        } else {
          cp.zoomTo(fit);
          slider.value = 1;
          labelZoom(1);
        }
      },

      /* impede zoom-out além do mínimo (fit) */
      zoom(e){
        if (e.detail.ratio < fit){ e.preventDefault(); cp.zoomTo(fit); }
        const rel = e.detail.ratio/fit;
        $('#zoom-slider').value = rel.toFixed(2);
        labelZoom(rel);
      }
    });
  }

  /* -------- slider -------- */
  window.updateImageScale = v => { cp?.zoomTo(fit*parseFloat(v)); labelZoom(v); };
  const labelZoom = r => $('#zoom-value').textContent = Math.round(r*100)+'%';

  /* -------- abrir -------- */
  window.openCropModal = idx =>{
    window.currentCropIndex = idx; window.isExistingPhoto=false;
    $('#crop-image').src = document.querySelector(`[data-photo-index="${idx}"] img`).src;
    $('#crop-modal').style.display='flex';
    $('#crop-image').onload = () => startCropper(null);
  };
  window.openCropModalExisting = (i,url,d={}) =>{
    window.currentCropIndex = i; window.isExistingPhoto=true;
    $('#crop-image').src = url;
    $('#crop-modal').style.display='flex';
    $('#crop-image').onload = () => startCropper(d);
  };

  /* -------- salvar -------- */
  window.saveCropPosition = () => {
  if (!cp) return;

  /* Canvas = imagem já renderizada no container ------------------- */
  const cv = cp.getCanvasData();       // left, top, width, height
  const bx = cp.getData(true);         // x,y,w,h em coord. originais
  const natW = cp.getImageData().naturalWidth;   // largura original

  const absZoom = cv.width / natW;     // zoom absoluto usado

  const payload = {
    index : window.currentCropIndex,

    /*  ► para reabrir depois */
    x     : Math.round(cv.left),
    y     : Math.round(cv.top),
    scale : +absZoom.toFixed(4),

    /*  ► esperado pelo backend */
    crop_x           : Math.round(cv.left),
    crop_y           : Math.round(cv.top),
    crop_scale       : +absZoom.toFixed(4),
    crop_data_x      : Math.round(bx.x),
    crop_data_y      : Math.round(bx.y),
    crop_data_width  : Math.round(bx.width),
    crop_data_height : Math.round(bx.height)
  };

  @this.call(
    window.isExistingPhoto ? 'saveExistingCropData' : 'saveCropData',
    payload
  );

  closeCropModal();
};

  /* -------- fechar -------- */
  window.closeCropModal = () => { $('#crop-modal').style.display='none'; cp?.destroy(); };

  /* — reatacha se Livewire recompilar enquanto modal aberto — */
  document.addEventListener('livewire:updated',()=>{
    if($('#crop-modal').style.display==='flex' && $('#crop-image').complete) startCropper(null);
  });

  // Listener para o evento de atualização da imagem cortada
  document.addEventListener('image-cropped', event => {
    const { index, url } = event.detail;
    // Tenta encontrar tanto em fotos existentes quanto em novas fotos
    const existingImg = document.querySelector(`[data-existing-photo-index="${index}"] img`);
    const newImg = document.querySelector(`[data-photo-index="${index}"] img`);
    
    if (existingImg) {
      existingImg.src = url;
    }
    if (newImg) {
      newImg.src = url;
    }
  });
}
</script>

</div>
