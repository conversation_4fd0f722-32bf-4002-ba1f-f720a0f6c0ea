## Test Enforcement

- Every change must be programmatically tested. Write a new test or update an existing test, then run the affected tests to make sure they pass.
- Run the minimum number of tests needed to ensure code quality and speed. Use ___SINGLE_BACKTICK___php artisan test___SINGLE_BACKTICK___ with a specific filename or filter.
<?php /**PATH C:\Users\<USER>\Herd\loja\storage\framework\views/41dc6ae0f9743bc468d8f2781365960b.blade.php ENDPATH**/ ?>