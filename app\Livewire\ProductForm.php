<?php

namespace App\Livewire;

use App\Models\Product;
use Livewire\Component;
use Livewire\WithFileUploads;
use Illuminate\Support\Facades\Storage;

class ProductForm extends Component
{
    // Temporariamente comentado para teste
    // use WithFileUploads;

    public $product;
    public $marca;
    public $cor;
    public $tamanho;
    public $caracteristicas;
    public $estado;
    public $descricao;
    public $valor;
    // public $photos = [];
    // public $existingPhotos = [];
    public $isEditMode = false;

    // Temporariamente comentado para teste
    // protected $rules = [
    //     'marca' => 'required|string|max:255|min:2',
    //     'cor' => 'required|string|max:255|min:2',
    //     'tamanho' => 'required|in:PP,P,M,G,G1',
    //     'caracteristicas' => 'required|string|min:10|max:1000',
    //     'estado' => 'required|in:Nova,Seminova,Usada',
    //     'descricao' => 'required|string|min:10|max:2000',
    //     'valor' => 'required|string',
    //     'photos.*' => 'nullable|image|mimes:jpeg,jpg,png,webp|max:2048',
    // ];

    // Temporariamente comentado para teste
    // protected $messages = [
    //     'marca.required' => 'A marca é obrigatória.',
    //     'marca.min' => 'A marca deve ter pelo menos 2 caracteres.',
    //     'cor.required' => 'A cor é obrigatória.',
    //     'cor.min' => 'A cor deve ter pelo menos 2 caracteres.',
    //     'caracteristicas.min' => 'As características devem ter pelo menos 10 caracteres.',
    //     'caracteristicas.max' => 'As características não podem exceder 1000 caracteres.',
    //     'descricao.min' => 'A descrição deve ter pelo menos 10 caracteres.',
    //     'descricao.max' => 'A descrição não pode exceder 2000 caracteres.',
    //     'valor.required' => 'O valor é obrigatório.',
    //     'valor.regex' => 'O valor deve estar no formato correto (ex: 10,50 ou 10.50).',
    //     'valor.min' => 'O valor deve ser maior que zero.',
    //     'photos.*.image' => 'O arquivo deve ser uma imagem.',
    //     'photos.*.mimes' => 'A imagem deve ser do tipo: jpeg, jpg, png ou webp.',
    //     'photos.*.max' => 'A imagem não pode ser maior que 2MB.',
    // ];

    // Temporariamente comentado para teste
    // public function updated($propertyName)
    // {
    //     $this->validateOnly($propertyName);
    // }

    // Temporariamente removido para teste
    // public function mount(?Product $product = null)
    // {
    //     $this->product = new Product();
    //     $this->tamanho = 'M';
    //     $this->estado = 'Nova';
    // }

    public function save()
    {
        // Versão simplificada para teste
        session()->flash('message', 'Teste de salvamento funcionando!');
    }

    /**
     * Normalize price format (accepts both comma and dot)
     */
    private function normalizePrice(string $price): float
    {
        // Remove espaços e caracteres não numéricos exceto vírgula e ponto
        $price = preg_replace('/[^\d,.]/', '', $price);

        // Se contém vírgula, substitui por ponto
        if (strpos($price, ',') !== false) {
            $price = str_replace(',', '.', $price);
        }

        return (float) $price;
    }

    /**
     * Clean and sanitize input data
     */
    private function sanitizeInputs(): void
    {
        $this->marca = trim($this->marca ?? '');
        $this->cor = trim($this->cor ?? '');
        $this->caracteristicas = trim($this->caracteristicas ?? '');
        $this->descricao = trim($this->descricao ?? '');
        $this->valor = trim($this->valor ?? '');
    }

    public function removePhoto($photoId)
    {
        if (!$this->product || !$this->product->exists) {
            session()->flash('error', 'Produto não encontrado.');
            return;
        }

        try {
            $image = $this->product->images()->findOrFail($photoId);

            // Verifica se o arquivo existe antes de tentar deletar
            if (Storage::disk('public')->exists($image->path)) {
                Storage::disk('public')->delete($image->path);
            }

            $image->delete();
            // $this->existingPhotos = $this->product->images()->get();
            session()->flash('message', 'Foto removida com sucesso.');
        } catch (\Exception $e) {
            session()->flash('error', 'Erro ao remover foto: ' . $e->getMessage());
        }
    }

    public function render()
    {
        return view('livewire.product-form');
    }
}
